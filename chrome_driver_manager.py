"""
Chrome Driver Otomatik Yönetim Modülü
Bu modül farklı Chrome sürümleri ile uyumlu çalışacak şekilde tasarlanmıştır.
Otomatik driver indirme ve güncelleme özelliği vardır.
"""

import os
import sys
import json
import shutil
import zipfile
import logging
import requests
import subprocess
import time
import threading
from pathlib import Path
from typing import Optional, Dict, Any
import psutil

try:
    import undetected_chromedriver as uc
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError as e:
    logging.error(f"Gerekli kütüphaneler yüklü değil: {e}")
    sys.exit(1)


class ChromeDriverManager:
    """Chrome Driver otomatik yönetim sınıfı"""
    
    def __init__(self, base_dir: Optional[str] = None):
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self.portable_chrome_dir = self.base_dir / "portable_chromium"
        self.chrome_browser_dir = self.portable_chrome_dir / "browser" / "chrome-win"
        self.chrome_driver_dir = self.portable_chrome_dir / "driver" / "chromedriver_win32"
        self.config_file = self.base_dir / "chrome_config.json"
        self.lock = threading.Lock()
        
        # Dizinleri oluştur
        self.chrome_browser_dir.mkdir(parents=True, exist_ok=True)
        self.chrome_driver_dir.mkdir(parents=True, exist_ok=True)
        
        # Logging ayarları
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def get_system_chrome_version(self) -> Optional[str]:
        """Sistemde yüklü Chrome sürümünü bul"""
        try:
            # Windows için Chrome sürüm kontrolü
            chrome_paths = [
                "C:/Program Files/Google/Chrome/Application/chrome.exe",
                "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
                os.path.expanduser("~/AppData/Local/Google/Chrome/Application/chrome.exe")
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    try:
                        result = subprocess.run(
                            [chrome_path, "--version"], 
                            capture_output=True, 
                            text=True, 
                            timeout=10,
                            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                        )
                        if result.returncode == 0:
                            version_line = result.stdout.strip()
                            # "Google Chrome 120.0.6099.224" formatından sürümü çıkar
                            import re
                            version_match = re.search(r'Chrome (\d+)', version_line)
                            if version_match:
                                main_version = version_match.group(1)
                                self.logger.info(f"Sistem Chrome sürümü bulundu: {version_line} (Ana sürüm: {main_version})")
                                return main_version
                            else:
                                # Alternatif parsing
                                words = version_line.split()
                                for word in words:
                                    if '.' in word and word.replace('.', '').isdigit():
                                        main_version = word.split('.')[0]
                                        if main_version.isdigit():
                                            self.logger.info(f"Sistem Chrome sürümü bulundu (alternatif): {version_line} (Ana sürüm: {main_version})")
                                            return main_version
                    except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                        self.logger.warning(f"Chrome sürüm kontrolü başarısız: {e}")
                        continue
            
            self.logger.warning("Sistemde Chrome bulunamadı")
            return None
            
        except Exception as e:
            self.logger.error(f"Chrome sürüm kontrolü hatası: {e}")
            return None
    
    def get_latest_chrome_version(self) -> Optional[str]:
        """En güncel Chrome sürümünü API'den al"""
        try:
            url = "https://versionhistory.googleapis.com/v1/chrome/platforms/win/channels/stable/versions"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if 'versions' in data and data['versions']:
                latest_version = data['versions'][0]['version']
                main_version = latest_version.split('.')[0]
                self.logger.info(f"En güncel Chrome sürümü: {latest_version} (Ana sürüm: {main_version})")
                return main_version
            
        except Exception as e:
            self.logger.error(f"Chrome sürüm API hatası: {e}")
            
        return None
    
    def download_portable_chrome(self, version: str) -> bool:
        """Taşınabilir Chrome indir"""
        try:
            # Chrome for Testing API'sini kullan
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            # İstenen sürümü bul - esnek eşleştirme
            chrome_download_url = None
            best_match_version = None
            
            for ver_info in data.get('versions', []):
                ver_string = ver_info['version']
                ver_major = ver_string.split('.')[0]
                
                # Tam eşleşme öncelikli
                if ver_major == version:
                    downloads = ver_info.get('downloads', {})
                    if 'chrome' in downloads:
                        for download in downloads['chrome']:
                            if download['platform'] == 'win64':
                                chrome_download_url = download['url']
                                best_match_version = ver_string
                                break
                        if chrome_download_url:
                            break
            
            # Eğer bulamazsak, en yakın sürümü dene
            if not chrome_download_url:
                self.logger.warning(f"Chrome {version} sürümü bulunamadı, en yakın sürümü arıyorum...")
                
                # Mevcut tüm sürümleri listele
                available_versions = []
                for ver_info in data.get('versions', []):
                    ver_string = ver_info['version']
                    ver_major = int(ver_string.split('.')[0])
                    downloads = ver_info.get('downloads', {})
                    
                    if 'chrome' in downloads:
                        for download in downloads['chrome']:
                            if download['platform'] == 'win64':
                                available_versions.append((ver_major, ver_string, download['url']))
                                break
                
                # En yakın sürümü bul
                target_version = int(version) if version.isdigit() else 120
                available_versions.sort(key=lambda x: abs(x[0] - target_version))
                
                if available_versions:
                    _, best_match_version, chrome_download_url = available_versions[0]
                    self.logger.info(f"En yakın Chrome sürümü bulundu: {best_match_version}")
            
            if not chrome_download_url:
                self.logger.error(f"Chrome için uygun indirme linki bulunamadı")
                return False
            
            # Chrome'u indir
            self.logger.info(f"Chrome {best_match_version} indiriliyor...")
            zip_path = self.portable_chrome_dir / f"chrome_{best_match_version}.zip"
            
            with requests.get(chrome_download_url, stream=True, timeout=60) as r:
                r.raise_for_status()
                total_size = int(r.headers.get('content-length', 0))
                downloaded = 0
                
                with open(zip_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(f"\rChrome indiriliyor: {progress:.1f}%", end='', flush=True)
            
            print()  # Yeni satır
            
            # ZIP dosyasını çıkar
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.portable_chrome_dir / "browser")
            
            # Zip dosyasını sil
            zip_path.unlink()
            
            self.logger.info(f"Chrome {best_match_version} başarıyla indirildi")
            return True
            
        except Exception as e:
            self.logger.error(f"Chrome indirme hatası: {e}")
            return False
    
    def download_chromedriver(self, version: str) -> bool:
        """ChromeDriver indir"""
        try:
            # Chrome for Testing API'sini kullan
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            # İstenen sürümü bul - esnek eşleştirme
            driver_download_url = None
            best_match_version = None
            
            for ver_info in data.get('versions', []):
                ver_string = ver_info['version']
                ver_major = ver_string.split('.')[0]
                
                # Tam eşleşme öncelikli
                if ver_major == version:
                    downloads = ver_info.get('downloads', {})
                    if 'chromedriver' in downloads:
                        for download in downloads['chromedriver']:
                            if download['platform'] == 'win64':
                                driver_download_url = download['url']
                                best_match_version = ver_string
                                break
                        if driver_download_url:
                            break
            
            # Eğer bulamazsak, en yakın sürümü dene
            if not driver_download_url:
                self.logger.warning(f"ChromeDriver {version} sürümü bulunamadı, en yakın sürümü arıyorum...")
                
                # Mevcut tüm sürümleri listele
                available_versions = []
                for ver_info in data.get('versions', []):
                    ver_string = ver_info['version']
                    ver_major = int(ver_string.split('.')[0])
                    downloads = ver_info.get('downloads', {})
                    
                    if 'chromedriver' in downloads:
                        for download in downloads['chromedriver']:
                            if download['platform'] == 'win64':
                                available_versions.append((ver_major, ver_string, download['url']))
                                break
                
                # En yakın sürümü bul
                target_version = int(version) if version.isdigit() else 120
                available_versions.sort(key=lambda x: abs(x[0] - target_version))
                
                if available_versions:
                    _, best_match_version, driver_download_url = available_versions[0]
                    self.logger.info(f"En yakın ChromeDriver sürümü bulundu: {best_match_version}")
            
            if not driver_download_url:
                self.logger.error(f"ChromeDriver için uygun indirme linki bulunamadı")
                return False
            
            # ChromeDriver'ı indir
            self.logger.info(f"ChromeDriver {best_match_version} indiriliyor...")
            zip_path = self.portable_chrome_dir / f"chromedriver_{best_match_version}.zip"
            
            with requests.get(driver_download_url, stream=True, timeout=60) as r:
                r.raise_for_status()
                with open(zip_path, 'wb') as f:
                    for chunk in r.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            # ZIP dosyasını çıkar
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.chrome_driver_dir.parent)
            
            # Zip dosyasını sil
            zip_path.unlink()
            
            self.logger.info(f"ChromeDriver {best_match_version} başarıyla indirildi")
            return True
            
        except Exception as e:
            self.logger.error(f"ChromeDriver indirme hatası: {e}")
            return False
    
    def load_config(self) -> Dict[str, Any]:
        """Konfigürasyon dosyasını yükle"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Konfigürasyon yüklenemedi: {e}")
        
        return {}
    
    def save_config(self, config: Dict[str, Any]):
        """Konfigürasyon dosyasını kaydet"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Konfigürasyon kaydedilemedi: {e}")
    
    def setup_chrome_environment(self) -> bool:
        """Chrome ortamını kurulum"""
        with self.lock:
            try:
                config = self.load_config()
                
                # Mevcut Chrome sürümünü kontrol et
                system_version = self.get_system_chrome_version()
                latest_version = self.get_latest_chrome_version()
                
                # Hangi sürümü kullanacağımızı belirle
                target_version = system_version if system_version else latest_version
                
                if not target_version:
                    self.logger.error("Chrome sürümü belirlenemedi")
                    return False
                
                # Mevcut kurulumu kontrol et
                current_version = config.get('chrome_version')
                chrome_exe = self.chrome_browser_dir / "chrome.exe"
                driver_exe = self.chrome_driver_dir / "chromedriver.exe"
                
                need_update = (
                    current_version != target_version or
                    not chrome_exe.exists() or
                    not driver_exe.exists()
                )
                
                if need_update:
                    self.logger.info(f"Chrome ortamı güncelleniyor: {current_version} -> {target_version}")
                    
                    # Chrome'u indir
                    if not self.download_portable_chrome(target_version):
                        self.logger.error("Chrome indirilemedi")
                        return False
                    
                    # ChromeDriver'ı indir
                    if not self.download_chromedriver(target_version):
                        self.logger.error("ChromeDriver indirilemedi")
                        return False
                    
                    # Konfigürasyonu güncelle
                    config['chrome_version'] = target_version
                    config['last_update'] = time.time()
                    self.save_config(config)
                    
                    self.logger.info(f"Chrome ortamı başarıyla güncellendi: {target_version}")
                
                return True
                
            except Exception as e:
                self.logger.error(f"Chrome ortam kurulum hatası: {e}")
                return False
    
    def kill_chrome_processes(self, profile_path: Optional[str] = None):
        """Chrome işlemlerini sonlandır"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    if profile_path:
                        # Belirli bir profil için çalışan Chrome'u sonlandır
                        if profile_path in ' '.join(proc.info['cmdline'] or []):
                            self.logger.info(f"Chrome işlemi sonlandırılıyor (PID: {proc.info['pid']})")
                            proc.terminate()
                            proc.wait(timeout=5)
                    else:
                        # Tüm Chrome işlemlerini sonlandır
                        self.logger.info(f"Chrome işlemi sonlandırılıyor (PID: {proc.info['pid']})")
                        proc.terminate()
                        proc.wait(timeout=5)
        except Exception as e:
            self.logger.warning(f"Chrome işlem sonlandırma hatası: {e}")
    
    def create_driver(self, account_username: str, headless: bool = True, 
                     custom_options: Optional[list] = None) -> Optional[webdriver.Chrome]:
        """Güvenilir Chrome driver oluştur"""
        
        # Chrome ortamını kur
        if not self.setup_chrome_environment():
            self.logger.error("Chrome ortamı kurulamadı")
            return None
        
        # Profil dizini
        profile_dir = self.base_dir / f"chrome_profile_{account_username}"
        profile_dir.mkdir(exist_ok=True)
        
        # Mevcut Chrome işlemlerini sonlandır
        self.kill_chrome_processes(str(profile_dir))
        
        # Chrome seçenekleri - undetected_chromedriver uyumlu
        options = uc.ChromeOptions()
        
        # Temel seçenekler
        options.add_argument(f'--user-data-dir={profile_dir}')
        if headless:
            options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--remote-debugging-port=0')
        options.add_argument('--lang=en-US')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')
        
        # Özel seçenekler
        if custom_options:
            for option in custom_options:
                options.add_argument(option)
        
        # Tercihleri ayarla - undetected_chromedriver uyumlu format
        try:
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2
            }
            options.add_experimental_option("prefs", prefs)
        except Exception:
            # Prefs sorun çıkarırsa atla
            pass
        
        # Taşınabilir Chrome yolunu ayarla
        chrome_exe = self.chrome_browser_dir / "chrome.exe"
        if chrome_exe.exists():
            options.binary_location = str(chrome_exe)
        
        # ChromeDriver yolunu ayarla
        driver_exe = self.chrome_driver_dir / "chromedriver.exe"
        
        # Driver oluşturma denemeleri
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"Chrome driver oluşturuluyor: {account_username} (Deneme {attempt + 1}/{max_retries})")
                
                # 1. Öncelik: undetected-chromedriver with minimal options
                try:
                    # Minimal options ile başla
                    minimal_options = uc.ChromeOptions()
                    minimal_options.add_argument(f'--user-data-dir={profile_dir}')
                    if headless:
                        minimal_options.add_argument('--headless=new')
                    minimal_options.add_argument('--disable-gpu')
                    minimal_options.add_argument('--no-sandbox')
                    minimal_options.add_argument('--disable-dev-shm-usage')
                    minimal_options.add_argument('--remote-debugging-port=0')
                    
                    # Taşınabilir Chrome kullan
                    if chrome_exe.exists():
                        minimal_options.binary_location = str(chrome_exe)
                    
                    if driver_exe.exists():
                        driver = uc.Chrome(
                            driver_executable_path=str(driver_exe),
                            options=minimal_options,
                            version_main=None
                        )
                    else:
                        driver = uc.Chrome(options=minimal_options, version_main=None)
                    
                    self.logger.info(f"Driver başarıyla oluşturuldu (undetected-chromedriver): {account_username}")
                    return driver
                    
                except Exception as uc_error:
                    self.logger.warning(f"undetected-chromedriver hatası: {uc_error}")
                
                # 2. Yedek: Standard Selenium WebDriver
                try:
                    # Standard selenium options
                    std_options = Options()
                    std_options.add_argument(f'--user-data-dir={profile_dir}')
                    if headless:
                        std_options.add_argument('--headless=new')
                    std_options.add_argument('--disable-gpu')
                    std_options.add_argument('--no-sandbox')
                    std_options.add_argument('--disable-dev-shm-usage')
                    std_options.add_argument('--disable-notifications')
                    std_options.add_argument('--remote-debugging-port=0')
                    
                    # Tercihleri ekle
                    prefs = {
                        "profile.default_content_setting_values.notifications": 2,
                        "profile.default_content_settings.popups": 0,
                        "profile.managed_default_content_settings.images": 2
                    }
                    std_options.add_experimental_option("prefs", prefs)
                    
                    # Taşınabilir Chrome kullan
                    if chrome_exe.exists():
                        std_options.binary_location = str(chrome_exe)
                    
                    if driver_exe.exists():
                        service = Service(str(driver_exe))
                    else:
                        service = Service(ChromeDriverManager().install())
                    
                    driver = webdriver.Chrome(service=service, options=std_options)
                    self.logger.info(f"Driver başarıyla oluşturuldu (standard selenium): {account_username}")
                    return driver
                    
                except Exception as selenium_error:
                    self.logger.warning(f"Standard selenium hatası: {selenium_error}")
                
                # 3. Son deneme: Chrome for Testing
                try:
                    service = Service(ChromeDriverManager(chrome_type=ChromeType.CHROMIUM).install())
                    
                    # En minimal seçenekler
                    basic_options = Options()
                    if headless:
                        basic_options.add_argument('--headless=new')
                    basic_options.add_argument('--no-sandbox')
                    basic_options.add_argument('--disable-dev-shm-usage')
                    
                    driver = webdriver.Chrome(service=service, options=basic_options)
                    self.logger.info(f"Driver başarıyla oluşturuldu (Chrome for Testing): {account_username}")
                    return driver
                    
                except Exception as ct_error:
                    self.logger.error(f"Chrome for Testing hatası: {ct_error}")
                
                # Deneme arasında bekleme
                if attempt < max_retries - 1:
                    time.sleep(2 + attempt)
                    
            except Exception as e:
                self.logger.error(f"Driver oluşturma hatası (Deneme {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 + attempt)
        
        self.logger.error(f"Tüm denemelar başarısız oldu: {account_username}")
        return None


# Global instance
_chrome_manager = None

def get_chrome_manager(base_dir: Optional[str] = None) -> ChromeDriverManager:
    """Global Chrome Manager instance'ını al"""
    global _chrome_manager
    if _chrome_manager is None:
        _chrome_manager = ChromeDriverManager(base_dir)
    return _chrome_manager

def create_driver_for_account(account_username: str, headless: bool = True, 
                             custom_options: Optional[list] = None) -> Optional[webdriver.Chrome]:
    """Hesap için Chrome driver oluştur - ana fonksiyon"""
    manager = get_chrome_manager()
    return manager.create_driver(account_username, headless, custom_options) 