import json
import logging
import os
import sys
import threading
from collections import deque
from datetime import datetime

from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt, QTimer, QMetaObject, Q_ARG
from PyQt5.QtGui import QCursor, QColor, QFont
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWidgets import QDialog, QGraphicsDropShadowEffect, QVBoxLayout, QFrame, QHBoxLayout, QLabel, QPushButton, \
    QWidget, QFormLayout, QLineEdit, QDesktopWidget, QPlainTextEdit, QMainWindow, QApplication
from apscheduler.schedulers.background import BackgroundScheduler

from utils import html_content, setup_logging, create_directory, clean_temp_files, protect_media_files, safe_file_delete, \
    get_skeleton_html, format_live_feed_event, live_feed_manager, reload_scheduler_helper, \
    TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID

from download import download_all_profiles_links
from stats import send_telegram_message, initialize_stats_system, check_profiles, update_stats_display_html, \
    stats_system, format_twitter_stats_as_boxes, format_instagram_stats_as_two_columns
from upload import schedule_all_profiles_uploads, upload_all_profiles_links


class JsBridge(QObject):
    def __init__(self, mainWindow):
        super().__init__()
        self.mainWindow = mainWindow
        self.dragPos = None
        self.windowPos = None
        self.destekDialog = None  # referans baştan ekle

    @pyqtSlot()
    def minimizeWindow(self):
        self.mainWindow.showMinimized()

    @pyqtSlot()
    def maximizeWindow(self):
        view = self.mainWindow.view
        # Eğer zaten max durumdaysa: normal moda dön ve kaydedilmiş deck boyutlarını geri yükle
        if self.mainWindow.isMaximized():
            self.mainWindow.showNormal()
            view.page().runJavaScript("""
                if (window._manualDeckSizes) {
                    const [l, m, r, e] = window._manualDeckSizes;
                    document.querySelector('.grid-container')
                        .style.gridTemplateColumns = `${l}px 4px ${m}px 4px ${r}px 4px ${e}px`;
                }
            """)
        else:
            # Max moda geçmeden önce şimdiki deck genişliklerini kaydet
            view.page().runJavaScript("""
                // Eğer mouse ile ayarlanmışsa, onları kaydet
                if (!window._manualDeckSizes) {
                    window._manualDeckSizes = [
                        document.querySelector('.left-deck').offsetWidth,
                        document.querySelector('.middle-deck').offsetWidth,
                        document.querySelector('.right-deck').offsetWidth,
                        document.querySelector('.extra-deck').offsetWidth
                    ];
                } else {
                    // Son mouse ile ayarlanan değerleri güncelle
                    window._manualDeckSizes = [
                        document.querySelector('.left-deck').offsetWidth,
                        document.querySelector('.middle-deck').offsetWidth,
                        document.querySelector('.right-deck').offsetWidth,
                        document.querySelector('.extra-deck').offsetWidth
                    ];
                }
                // Tam ekran için orantılı (fr) layout'a geri dön
                document.querySelector('.grid-container')
                    .style.gridTemplateColumns = '0.64fr 4px 1fr 4px 1fr 4px 1fr';
            """)
            self.mainWindow.showMaximized()

        # Mouse ile boyut değiştirildiğinde _manualDeckSizes güncellensin
        view.page().runJavaScript("""
            function updateManualDeckSizes() {
                window._manualDeckSizes = [
                    document.querySelector('.left-deck').offsetWidth,
                    document.querySelector('.middle-deck').offsetWidth,
                    document.querySelector('.right-deck').offsetWidth,
                    document.querySelector('.extra-deck').offsetWidth
                ];
            }
            // Resizer event'lerine ekle
            ['resizer-1','resizer-2','resizer-3'].forEach(function(id) {
                var resizer = document.getElementById(id);
                if (resizer && !resizer._deckSizeListener) {
                    resizer.addEventListener('mouseup', updateManualDeckSizes);
                    resizer._deckSizeListener = true;
                }
            });
        """)

    @pyqtSlot()
    def closeWindow(self):
        self.mainWindow.close()

    @pyqtSlot()
    def startDrag(self):
        self.dragPos = QCursor.pos()
        self.windowPos = self.mainWindow.pos()

    @pyqtSlot()
    def dragWindow(self):
        if self.dragPos is not None and self.windowPos is not None:
            delta = QCursor.pos() - self.dragPos
            self.mainWindow.move(self.windowPos + delta)

    @pyqtSlot()
    def stopDrag(self):
        self.dragPos = None
        self.windowPos = None

    @pyqtSlot()
    def openDestekWindow(self):
        if getattr(self, 'destekDialog', None) is not None:
            self.destekDialog.close()
            self.destekDialog = None
        self.destekDialog = DestekDialog(self.mainWindow)
        self.destekDialog.destroyed.connect(lambda: setattr(self, 'destekDialog', None))
        self.destekDialog.show()

    @pyqtSlot()
    def startProcessingFromJS(self):
        self.mainWindow.startProcessing()

    @pyqtSlot()
    def stopProcessing(self):
        self.mainWindow.stopProcessing()

    @pyqtSlot()
    def syncProfilesWithJSON(self):
        """JavaScript'ten çağrıldığında, MainWindow'daki sync_profiles_with_json metodunu çağırır"""
        if hasattr(self.mainWindow, 'sync_profiles_with_json'):
            self.mainWindow.sync_profiles_with_json()


class ProfileEditorDialog(QDialog):
    """
    Profil düzenleme penceresi (Pop-up).
    Saat ayarları için "Saat Ayarları" butonu,
    Link Ekle için "Link Ekle" butonu,
    Kaydet/kapat için alt bar vs.
    """
    profileNameChanged = pyqtSignal(str, str)  # (profilePath, newProfileName)

    def __init__(self, profilePath, parent=None):
        super().__init__(parent)
        self.profilePath = profilePath
        try:
            with open(self.profilePath, "r", encoding="utf-8") as f:
                self.data = json.load(f)
        except Exception as e:
            self.data = {}
            logging.error(f"JSON okunurken hata: {e}")

        # Kullanıcı adı yoksa, dosya adı ile gösterelim
        self.profileName = self.data.get("username", "").strip()

        # schedule verisi (gün bazlı saatler)
        if "schedule" not in self.data:
            self.data["schedule"] = {
                "monday": [],
                "tuesday": [],
                "wednesday": [],
                "thursday": [],
                "friday": [],
                "saturday": [],
                "sunday": [],
            }

        # links verisi (tek liste)
        if "links" not in self.data:
            self.data["links"] = []

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setWindowModality(Qt.ApplicationModal)
        self.setModal(True)
        self.resize(314, 360)
        self.centerDialog()

        shadow = QGraphicsDropShadowEffect(self)
        shadow.setOffset(0, 0)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 180))
        self.setGraphicsEffect(shadow)

        self.mainLayout = QVBoxLayout(self)
        self.mainLayout.setContentsMargins(0, 0, 0, 0)
        self.mainLayout.setSpacing(0)

        # ------------------ Başlık Çubuğu ------------------
        self.titleBar = QFrame()
        self.titleBar.setObjectName("titleBar")
        self.titleBar.setFixedHeight(32)
        self.titleBarLayout = QHBoxLayout(self.titleBar)
        self.titleBarLayout.setContentsMargins(8, 0, 8, 0)
        self.titleBarLayout.setSpacing(0)

        self.emptyLabel = QLabel("")
        self.titleBarLayout.addWidget(self.emptyLabel, 1)

        self.closeBtn = QPushButton("?")
        self.closeBtn.setFixedSize(24, 24)
        self.closeBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #2B2B2B;
                border: none;
                border-radius: 4px;
                color: #FFFFFF;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            """
        )
        self.closeBtn.clicked.connect(self.close)
        self.titleBarLayout.addWidget(self.closeBtn, 0, alignment=Qt.AlignRight)

        # ------------------ İçerik Bölgesi ------------------
        self.contentWidget = QWidget()
        self.contentLayout = QVBoxLayout(self.contentWidget)
        self.contentLayout.setContentsMargins(20, 6, 20, 16)
        self.contentLayout.setSpacing(8)
        self.contentLayout.setAlignment(Qt.AlignCenter)

        # ------------------ Profil Adı Konteyneri ve Etiketi ------------------
        # Dinamik olarak metne göre boyutlanan etiket,
        # fakat konteyner sabit yüksekliğe sahip; böylece diğer öğeler yer değiştirmez.
        self.profileNameLabel = QLabel(self.profileName if self.profileName else "")
        fontProfileName = QFont("Segoe UI", 13, QFont.Bold)
        self.profileNameLabel.setFont(fontProfileName)
        self.profileNameLabel.setAlignment(Qt.AlignCenter)
        if self.profileName:
            self.profileNameLabel.setStyleSheet("""
                color: black;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffe082,
                    stop: 0.5 #fff9c4,
                    stop: 1 #ffd54f
                );
                border: 1px solid #d6a300;
                border-radius: 6px;
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
        else:
            self.profileNameLabel.setStyleSheet("""
                background: transparent;
                border: none;
            """)

        # Konteyner widget'ı oluşturuyoruz
        self.profileNameContainer = QWidget()
        # Örneğin sabit 40 piksel yüksekliğinde (ihtiyaca göre ayarlayın)
        self.profileNameContainer.setFixedHeight(40)
        containerLayout = QHBoxLayout(self.profileNameContainer)
        containerLayout.setContentsMargins(0, 0, 0, 0)
        containerLayout.setAlignment(Qt.AlignCenter)
        containerLayout.addWidget(self.profileNameLabel)
        self.contentLayout.addWidget(self.profileNameContainer, 0, Qt.AlignCenter)
        self.contentLayout.addSpacing(2)

        self.infoLabel = QLabel("")
        self.infoLabel.setFixedHeight(18)
        self.infoLabel.setStyleSheet("color: #B3B3B3; font-size: 10px; margin-bottom: 2px;")
        self.infoLabel.setAlignment(Qt.AlignCenter)
        self.contentLayout.addWidget(self.infoLabel, 0, Qt.AlignCenter)

        # ------------------ Form Bölümü ------------------
        self.formLayoutContainer = QWidget()
        self.formLayoutContainerLayout = QVBoxLayout(self.formLayoutContainer)
        self.formLayoutContainerLayout.setContentsMargins(0, 0, 0, 0)
        self.formLayoutContainerLayout.setSpacing(5)
        self.formLayoutContainerLayout.setAlignment(Qt.AlignCenter)

        self.formLayout = QFormLayout()
        self.formLayout.setVerticalSpacing(8)
        self.formLayout.setHorizontalSpacing(15)
        self.formLayout.setAlignment(Qt.AlignCenter)
        self.formLayout.setFormAlignment(Qt.AlignCenter)
        self.formLayout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Kullanıcı Adı
        self.usernameLabel = QLabel("Kullanıcı Adı:")
        self.usernameLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.usernameEdit = QLineEdit()
        self.usernameEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
            padding: 5px;
        """
        )
        self.usernameEdit.setText(self.data.get("username", ""))
        self.usernameEdit.setFixedWidth(180)
        self.formLayout.addRow(self.usernameLabel, self.usernameEdit)
        self.usernameEdit.textChanged.connect(self.onUsernameChanged)

        # Şifre
        self.passwordLabel = QLabel("Şifre:")
        self.passwordLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.passwordContainer = QFrame()
        self.passwordContainer.setStyleSheet(
            """
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
        """
        )
        self.passwordContainer.setFixedWidth(180)
        pwContainerLayout = QHBoxLayout(self.passwordContainer)
        pwContainerLayout.setContentsMargins(0, 0, 0, 0)
        pwContainerLayout.setSpacing(0)
        self.passwordEdit = QLineEdit()
        self.passwordEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: transparent;
            border: none;
            padding: 5px;
        """
        )
        self.passwordEdit.setEchoMode(QLineEdit.Normal)
        self.passwordEdit.setText(self.data.get("password", ""))
        pwContainerLayout.addWidget(self.passwordEdit)
        self.formLayout.addRow(self.passwordLabel, self.passwordContainer)

        # Hashtags
        self.hashtagsLabel = QLabel("Hashtags:")
        self.hashtagsLabel.setStyleSheet("font: bold 12px 'Segoe UI'; color: white;")
        self.hashtagsEdit = QLineEdit()
        self.hashtagsEdit.setStyleSheet(
            """
            font: 11px 'Segoe UI';
            color: white;
            background-color: #1E1E1E;
            border: 1px solid #373737;
            border-radius: 4px;
            padding: 5px;
        """
        )
        self.hashtagsEdit.setFixedWidth(180)
        self.hashtagsEdit.setPlaceholderText("Örnek: #funny, #cat")
        hashtags_list = self.data.get("hashtags", [])
        self.hashtagsEdit.setText(",".join(hashtags_list))
        self.formLayout.addRow(self.hashtagsLabel, self.hashtagsEdit)

        self.formLayoutContainerLayout.addLayout(self.formLayout)
        self.formLayoutContainerLayout.addSpacing(15)

        # Saat Ayarları Butonu
        self.timeSettingsBtn = QPushButton("Saat Ayarları")
        self.timeSettingsBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #242424;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2196F3;
            }
        """
        )
        self.timeSettingsBtn.setFixedSize(120, 36)
        self.timeSettingsBtn.clicked.connect(self.openTimeSettingsDialog)
        self.formLayoutContainerLayout.addWidget(self.timeSettingsBtn, 0, Qt.AlignCenter)

        self.contentLayout.addWidget(self.formLayoutContainer, 0, Qt.AlignCenter)

        # Link Ekle Butonu
        self.linkEkleBtn = QPushButton("Link Ekle")
        self.linkEkleBtn.setStyleSheet(
            """
            QPushButton {
                background-color: #242424;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2196F3;
            }
        """
        )
        self.linkEkleBtn.setFixedSize(90, 36)
        self.linkEkleBtn.clicked.connect(self.openLinkSettingsDialog)
        self.contentLayout.addWidget(self.linkEkleBtn, 0, Qt.AlignCenter)
        self.contentLayout.addSpacing(12)

        # ------------------ Alt Bar (Kaydet/Kapat) ------------------
        self.bottomButtonWidget = QWidget()
        self.bottomButtonWidget.setFixedHeight(60)
        self.bottomButtonWidget.setStyleSheet(
            """
            background-color: #2A2A2A;
            border-top: 1px solid #373737;
        """
        )
        self.bottomButtonLayout = QHBoxLayout(self.bottomButtonWidget)
        self.bottomButtonLayout.setContentsMargins(10, 10, 10, 10)
        self.bottomButtonLayout.setSpacing(0)

        self.saveBtn = QPushButton("Kaydet")
        self.closeBtn2 = QPushButton("Kapat")
        buttonStyle = """
            QPushButton {
                background-color: #242424;
                border: 1px solid #444;
                border-radius: 6px;
                padding: 10px 20px;
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                min-width: 110px;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
            }
        """
        self.saveBtn.setStyleSheet(buttonStyle)
        self.closeBtn2.setStyleSheet(buttonStyle)
        self.saveBtn.clicked.connect(self.saveProfile)
        self.closeBtn2.clicked.connect(self.close)

        self.bottomButtonLayout.addWidget(self.saveBtn, alignment=Qt.AlignLeft)
        self.bottomButtonLayout.addStretch()
        self.bottomButtonLayout.addWidget(self.closeBtn2, alignment=Qt.AlignRight)

        self.contentLayout.addWidget(self.bottomButtonWidget)

        self.mainLayout.addWidget(self.titleBar)
        self.mainLayout.addWidget(self.contentWidget)

        self.setStyleSheet(
            """
            QDialog {
                background-color: #2A2A2A;
                color: #FFFFFF;
                font-family: 'Segoe UI', sans-serif;
                border: 2px solid #2196F3;
                border-radius: 8px;
            }
            QFormLayout QLabel {
                color: #FFFFFF !important;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Segoe UI', sans-serif;
            }
            QLineEdit {
                background-color: #2A2A2A;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 5px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QPushButton {
                background-color: #2B2B2B;
                border: 1px solid #373737;
                border-radius: 4px;
                padding: 6px 13px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
            }
            """
        )

        # Sürükleme değişkenleri
        self.dragPos = None
        self.draggingTitleBar = False

        # TitleBar olaylarını yakalayalım
        self.titleBar.mousePressEvent = self._titleBarMousePress
        self.titleBar.mouseReleaseEvent = self._titleBarMouseRelease
        self.titleBar.mouseMoveEvent = self._titleBarMouseMove

    def centerDialog(self):
        if self.parent():
            parent_center = self.parent().frameGeometry().center()
            self.move(parent_center.x() - self.width() // 2, parent_center.y() - self.height() // 2)
        else:
            frame_geom = self.frameGeometry()
            screen_center = QDesktopWidget().availableGeometry().center()
            frame_geom.moveCenter(screen_center)
            self.move(frame_geom.topLeft())

    def _titleBarMousePress(self, event):
        if event.button() == Qt.LeftButton:
            self.dragPos = event.globalPos()
            self.draggingTitleBar = True
        event.accept()

    def _titleBarMouseRelease(self, event):
        self.dragPos = None
        self.draggingTitleBar = False
        event.accept()

    def _titleBarMouseMove(self, event):
        if self.draggingTitleBar and self.dragPos is not None:
            delta = event.globalPos() - self.dragPos
            new_pos = self.pos() + delta
            self.move(new_pos)
            self.dragPos = event.globalPos()
        event.accept()

    def onUsernameChanged(self, newText):
        newTextStripped = newText.strip()
        name_to_emit = ""

        if not newTextStripped:
            # For the dialog's own label, make it appear empty or show a placeholder
            self.profileNameLabel.setText(" ")  # Or a placeholder like "[Kullanıcı Adı Yok]"
            self.profileNameLabel.setStyleSheet("""
                background: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                color: #888;
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
            name_to_emit = os.path.splitext(os.path.basename(self.profilePath))[0]
        else:
            self.profileNameLabel.setText(newTextStripped)
            self.profileNameLabel.setStyleSheet("""
                color: black;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffe082,
                    stop: 0.5 #fff9c4,
                    stop: 1 #ffd54f
                );
                border: 1px solid #d6a300;
                border-radius: 6px;
                padding: 6px 12px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 0.2px;
            """)
            name_to_emit = newTextStripped

        self.profileNameLabel.adjustSize()
        self.profileNameChanged.emit(self.profilePath, name_to_emit)

    def saveProfile(self):
        username_before = self.data.get("username", "").strip()
        password_before = self.data.get("password", "").strip()

        # Yeni değerleri al
        new_username = self.usernameEdit.text().strip()
        new_password = self.passwordEdit.text().strip()

        # Kullanıcı adı veya şifre silinmiş mi?
        credentials_removed = ((username_before and not new_username) or
                              (password_before and not new_password))

        # Yeni değerleri data'ya ata
        self.data["username"] = new_username
        self.data["password"] = new_password

        # Kullanıcı adı veya şifre silinmişse tüm profil içeriğini temizle
        if credentials_removed:
            # Profile ait tüm verileri sıfırla
            self.data = {
                "username": new_username,
                "password": new_password,
                "schedule": {
                    "monday": [],
                    "tuesday": [],
                    "wednesday": [],
                    "thursday": [],
                    "friday": [],
                    "saturday": [],
                    "sunday": [],
                },
                "hashtags": [],
                "links": [],
                "downloaded": []
            }

            # İndirilen dosyaları temizle
            try:
                base_dir = os.path.dirname(os.path.abspath(__file__))
                videos_dir = os.path.join(base_dir, "videos")

                # Önceki indirmeleri bul ve sil
                if username_before:
                    platform = os.path.basename(os.path.dirname(self.profilePath))
                    profile_key = f"{platform}_{username_before}"

                    # İstatistikleri temizle
                    if profile_key in stats_system.stats_data:
                        del stats_system.stats_data[profile_key]
                    if profile_key in stats_system.last_stats_update:
                        del stats_system.last_stats_update[profile_key]
                    if profile_key in stats_system.stats_display_queue:
                        stats_system.stats_display_queue.remove(profile_key)
                    if profile_key in stats_system.profiles_to_process:
                        stats_system.profiles_to_process.discard(profile_key)

                    if platform == "instagram":
                        # Instagram session dosyasını sil
                        session_dir = os.path.join(base_dir, "configuration", "instagram", "sessions")
                        if os.path.exists(session_dir):
                            session_file = os.path.join(session_dir, f"{username_before}_session.json")
                            meta_file = os.path.join(session_dir, f"{username_before}_session.meta.json")
                            for f in [session_file, meta_file]:
                                if os.path.exists(f):
                                    try:
                                        os.remove(f)
                                        logging.info(f"Profil temizleme: Session dosyası silindi: {f}")
                                    except Exception as e:
                                        logging.error(f"Session dosyası silinirken hata: {e}")

                    if platform == "twitter":
                        # Twitter chrome profil klasörünü silmeyi thread'e al
                        chrome_profile_dir = os.path.join(base_dir, f"chrome_profile_{username_before}")
                        if os.path.exists(chrome_profile_dir):
                            import shutil
                            import time
                            def remove_chrome_profile():
                                max_retries = 5
                                for attempt in range(max_retries):
                                    try:
                                        shutil.rmtree(chrome_profile_dir)
                                        logging.info(
                                            f"Profil temizleme: Chrome profil klasörü silindi: {chrome_profile_dir}")
                                        # Başarı mesajını UI thread'inde göster
                                        QMetaObject.invokeMethod(self.infoLabel, "setText", Qt.QueuedConnection, Q_ARG(str, "Chrome profil klasörü silindi!"))
                                        return
                                    except Exception as e:
                                        if attempt < max_retries - 1 and ("kullanıldığından" in str(e) or "used by another process" in str(e)):
                                            logging.warning(
                                                f"Chrome profil klasörü silinemedi, tekrar deneniyor... ({attempt + 1}/{max_retries})")
                                            time.sleep(2)
                                        else:
                                            logging.error(f"Chrome profil klasörü silinirken hata: {e}")
                                            # Hata mesajını UI thread'inde göster
                                            QMetaObject.invokeMethod(self.infoLabel, "setText", Qt.QueuedConnection, Q_ARG(str, f"Klasör silinemedi: {e}"))
                                            return
                                # 5 denemede de silinemezse hata mesajı
                                QMetaObject.invokeMethod(self.infoLabel, "setText", Qt.QueuedConnection, Q_ARG(str, "Klasör başka bir işlem tarafından kullanılıyor!"))
                            self.infoLabel.setText("Chrome profil klasörü siliniyor...")
                            threading.Thread(target=remove_chrome_profile, daemon=True).start()

                logging.info(f"Profil temizleme: Kullanıcı adı/şifre silindiği için profil içeriği tamamen temizlendi: {self.profilePath}")
            except Exception as e:
                logging.error(f"Profil temizleme işlemi sırasında hata: {e}")
        else:
            # Sadece hashtag bilgilerini güncelle
            hashtags_text = self.hashtagsEdit.text().strip()
            if hashtags_text:
                self.data["hashtags"] = [tag.strip() for tag in hashtags_text.split(",")]
            else:
                self.data["hashtags"] = []

        try:
            with open(self.profilePath, "w", encoding="utf-8") as f:
                json.dump(self.data, f, indent=4)
            self.infoLabel.setText("KAYDEDİLDİ!")

            # Determine the name to display in the list after save
            saved_username = self.data["username"]
            final_display_name_for_list = ""
            if not saved_username:
                final_display_name_for_list = os.path.splitext(os.path.basename(self.profilePath))[0]
            else:
                final_display_name_for_list = saved_username

            # Emit the final name to update the list
            self.profileNameChanged.emit(self.profilePath, final_display_name_for_list)

            # Can barlarını ve istatistikleri anında güncelle
            if self.parent() and hasattr(self.parent(), 'update_stats'):
                self.parent().update_stats()

            if self.parent() and hasattr(self.parent(), 'reload_scheduler'):
                self.parent().reload_scheduler()

            # Eğer profilde link varsa ve kullanıcı adı/şifre silinmemişse, anında indirme ve işleme işlemini başlat
            if self.data.get("links") and not credentials_removed:
                try:
                    # Thread'e alarak UI donmasını önle
                    def threaded_process():
                        process_links_immediately()

                    import threading
                    threading.Thread(target=threaded_process, daemon=True).start()
                    logging.info(f"Profil kaydı sonrası {len(self.data['links'])} link için indirme/işleme işlemi tetiklendi")
                except Exception as e:
                    logging.error(f"Profil kaydı sonrası indirme/işleme tetiklenemedi: {e}")

            self.infoLabel.setStyleSheet("font-weight: bold; color: #39FF14; font-size: 15px;")
            QTimer.singleShot(2500, lambda: self.infoLabel.setText(""))
        except Exception as e:
            self.infoLabel.setText(f"Hata: {str(e)}")
            self.infoLabel.setStyleSheet("color: #FF0000; font-size: 13px;")

    def openTimeSettingsDialog(self):
        if getattr(self, 'timeSettingsDialog', None) is not None:
            self.timeSettingsDialog.close()
            self.timeSettingsDialog = None
        self.timeSettingsDialog = self.TimeSettingsDialog(self.data["schedule"], self)
        self.timeSettingsDialog.destroyed.connect(lambda: setattr(self, 'timeSettingsDialog', None))
        self.timeSettingsDialog.show()

    def openLinkSettingsDialog(self):
        if getattr(self, 'linkSettingsDialog', None) is not None:
            self.linkSettingsDialog.close()
            self.linkSettingsDialog = None
        self.linkSettingsDialog = self.LinkSettingsDialog(self.data, self)
        self.linkSettingsDialog.destroyed.connect(lambda: setattr(self, 'linkSettingsDialog', None))

        # Link ekleme penceresi kapandığında değişiklikleri ana ekrana yansıt
        self.linkSettingsDialog.finished.connect(self.update_after_link_dialog)

        self.linkSettingsDialog.show()

    def update_after_link_dialog(self):
        """Link ekleme penceresi kapandıktan sonra UI'yi günceller."""
        if hasattr(self, 'linkSettingsDialog') and self.linkSettingsDialog and hasattr(self.linkSettingsDialog, 'changes_saved'):
            # Eğer link ekleme penceresinde değişiklik yapıldıysa, ana ekrandaki Kaydet butonunu görsel olarak vurgula
            if self.linkSettingsDialog.changes_saved:
                current_links = len(self.data.get("links", []))
                self.infoLabel.setText(f"{current_links} link kaydedildi ve indirme başladı!")
                self.infoLabel.setStyleSheet("font-weight: bold; color: #39FF14; font-size: 15px;")
                QTimer.singleShot(2500, lambda: self.infoLabel.setText(""))

    class TimeSettingsDialog(QDialog):
        """
        Saat ayarları penceresi (gün bazlı).
        Bu pencerede saatler, "21:43 - 23:41 - 23:55" formatında girilecektir.
        Yazı kutusuna girilen metin, satır sonuna gelince otomatik olarak aşağıya geçer.
        """

        def __init__(self, scheduleDict, parent=None):
            super().__init__(parent)
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
            self.setWindowModality(Qt.ApplicationModal)
            self.setModal(True)
            self.resize(400, 280)

            self.original_schedule = scheduleDict
            self.temp_schedule = json.loads(json.dumps(scheduleDict))
            self.changesSaved = False

            self.currentDay = "monday"

            shadow = QGraphicsDropShadowEffect(self)
            shadow.setOffset(0, 0)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 180))
            self.setGraphicsEffect(shadow)

            self.mainLayout = QVBoxLayout(self)
            self.mainLayout.setContentsMargins(15, 15, 15, 15)
            self.mainLayout.setSpacing(12)

            self.titleLabel = QLabel("Saat Ayarları")
            self.titleLabel.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                margin-bottom: 5px;
            """)
            self.titleLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.titleLabel)

            self.statusLabel = QLabel("")
            self.statusLabel.setStyleSheet("font-size: 12px; color: #39FF14;")
            self.statusLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.statusLabel)

            self.daySelector = DaySelector(self)
            self.mainLayout.addWidget(self.daySelector, 0, Qt.AlignCenter)
            self.daySelector.setCurrentDay(self.currentDay)
            self.daySelector.dayChanged.connect(self.onDayChanged)

            self.textEdit = QPlainTextEdit(self)
            self.textEdit.setPlaceholderText("Örnek: 21:43 - 23:41 - 23:55")
            self.textEdit.setStyleSheet("""
                background-color: #1E1E1E;
                border: 1px solid #373737;
                border-radius: 4px;
                color: #FFFFFF;
                font-size: 12px;
                font-family: 'Segoe UI';
                padding: 8px;
                selection-background-color: #2196F3;
                selection-color: #FFFFFF;
            """)
            self.textEdit.setLineWrapMode(QPlainTextEdit.WidgetWidth)
            self.mainLayout.addWidget(self.textEdit, 1)

            self.buttonLayout = QHBoxLayout()
            self.saveDayBtn = QPushButton("Kaydet")
            self.closeBtn = QPushButton("Kapat")

            buttonStyle = """
                QPushButton {
                    background-color: #2B2B2B;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                    border-color: #2196F3;
                }
            """
            self.saveDayBtn.setStyleSheet(buttonStyle)
            self.closeBtn.setStyleSheet(buttonStyle)

            self.saveDayBtn.clicked.connect(self.onSaveClicked)
            self.closeBtn.clicked.connect(self.close)

            self.buttonLayout.addStretch()
            self.buttonLayout.addWidget(self.saveDayBtn)
            self.buttonLayout.addWidget(self.closeBtn)
            self.buttonLayout.addStretch()
            self.mainLayout.addLayout(self.buttonLayout)

            self.setStyleSheet("""
                QDialog {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 1px solid #2196F3;
                    border-radius: 6px;
                }
            """)

            self.dragPos = None
            self.loadTimesForDay(self.currentDay)

        def mousePressEvent(self, event):
            if event.button() == Qt.LeftButton:
                self.dragPos = event.globalPos()
            super().mousePressEvent(event)

        def mouseReleaseEvent(self, event):
            self.dragPos = None
            super().mouseReleaseEvent(event)

        def mouseMoveEvent(self, event):
            if event.buttons() == Qt.LeftButton and self.dragPos is not None:
                delta = event.globalPos() - self.dragPos
                self.move(self.pos() + delta)
                self.dragPos = event.globalPos()
            super().mouseMoveEvent(event)

        def onDayChanged(self, newDay):
            self.saveCurrentDayTimes()
            self.currentDay = newDay
            self.loadTimesForDay(newDay)
            self.statusLabel.setText("")

        def loadTimesForDay(self, day):
            timesList = self.temp_schedule.get(day, [])
            self.textEdit.clear()
            if timesList:
                self.textEdit.setPlainText(" - ".join(timesList))

        def saveCurrentDayTimes(self):
            raw_text = self.textEdit.toPlainText().strip()
            if not raw_text:
                self.temp_schedule[self.currentDay] = []
            else:
                times = []
                for line in raw_text.split("\n"):
                    parts = line.split(" - ")
                    for part in parts:
                        t = part.strip()
                        if t:
                            times.append(t)
                self.temp_schedule[self.currentDay] = times

        def onSaveClicked(self):
            self.saveCurrentDayTimes()
            self.original_schedule.clear()
            self.original_schedule.update(self.temp_schedule)
            self.changesSaved = True
            self.statusLabel.setText("<b>KAYDEDİLDİ!</b>")

            # Scheduler'ı hemen yeniden yükle
            try:
                # Ana pencereye erişim için parent-parent ilişkisini kullan
                main_window = None
                if self.parent() and hasattr(self.parent(), 'parent'):
                    parent_parent = self.parent().parent()
                    if parent_parent and hasattr(parent_parent, 'reload_scheduler'):
                        main_window = parent_parent

                # Ana pencereyi bulamazsak, tüm üst seviye widget'ları kontrol et
                if not main_window:
                    from PyQt5.QtWidgets import QApplication
                    for widget in QApplication.topLevelWidgets():
                        if hasattr(widget, 'reload_scheduler'):
                            main_window = widget
                            break

                # Scheduler'ı yeniden yükle
                if main_window:
                    main_window.reload_scheduler()
                    logging.info("Saat ayarları kaydedildi ve scheduler anında yeniden yüklendi.")
            except Exception as e:
                logging.error(f"Saat ayarları kaydı sonrası scheduler yeniden yüklenemedi: {e}")

            QTimer.singleShot(2000, lambda: self.statusLabel.setText(""))

        def closeEvent(self, event):
            # Eğer değişiklikler kaydedildiyse scheduler'ı yeniden yükle
            if self.changesSaved:
                try:
                    # Ana pencereye erişim için parent-parent ilişkisini kullan
                    main_window = None
                    if self.parent() and hasattr(self.parent(), 'parent'):
                        parent_parent = self.parent().parent()
                        if parent_parent and hasattr(parent_parent, 'reload_scheduler'):
                            main_window = parent_parent

                    # Ana pencereyi bulamazsak, tüm üst seviye widget'ları kontrol et
                    if not main_window:
                        from PyQt5.QtWidgets import QApplication
                        for widget in QApplication.topLevelWidgets():
                            if hasattr(widget, 'reload_scheduler'):
                                main_window = widget
                                break

                    # Scheduler'ı yeniden yükle
                    if main_window:
                        main_window.reload_scheduler()
                        logging.info("Saat ayarları değişikliği sonrası scheduler yeniden yüklendi.")
                except Exception as e:
                    logging.error(f"Saat ayarları sonrası scheduler yeniden yüklenemedi: {e}")

            super().closeEvent(event)

    class LinkSettingsDialog(QDialog):
        """
        Link Ekleme Penceresi (gün bazlı değil).
        Tüm linkler tek listede saklanır (self.data["links"]).
        """

        def __init__(self, data, parent=None):
            super().__init__(parent)
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
            self.setWindowModality(Qt.ApplicationModal)
            self.setModal(True)
            self.resize(400, 280)

            self.data = data
            self.profile_path = None  # Profil dosya yolunu burada saklayacağız
            self.changes_saved = False  # Değişikliklerin kaydedilip kaydedilmediğini takip et

            # Eğer parent bir ProfileEditorDialog ise profil yolunu al
            if parent and hasattr(parent, 'profilePath'):
                self.profile_path = parent.profilePath

            shadow = QGraphicsDropShadowEffect(self)
            shadow.setOffset(0, 0)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 180))
            self.setGraphicsEffect(shadow)

            self.mainLayout = QVBoxLayout(self)
            self.mainLayout.setContentsMargins(15, 15, 15, 15)
            self.mainLayout.setSpacing(12)

            self.titleLabel = QLabel("Link Ekleme")
            self.titleLabel.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                margin-bottom: 5px;
            """)
            self.titleLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.titleLabel)

            self.statusLabel = QLabel("")
            self.statusLabel.setStyleSheet("font-size: 12px; color: #39FF14;")
            self.statusLabel.setAlignment(Qt.AlignCenter)
            self.mainLayout.addWidget(self.statusLabel)

            self.textEdit = QPlainTextEdit(self)
            self.textEdit.setPlaceholderText(
                "Her satıra bir link ekleyin.\nÖrnek:\nhttps://youtu.be/... \nhttps://twitter.com/...")
            self.textEdit.setStyleSheet("""
                background-color: #1E1E1E;
                border: 1px solid #373737;
                border-radius: 4px;
                color: #FFFFFF;
                font-size: 12px;
                font-family: 'Segoe UI';
                padding: 8px;
                selection-background-color: #2196F3;
                selection-color: #FFFFFF;
            """)
            self.textEdit.setLineWrapMode(QPlainTextEdit.WidgetWidth)
            self.mainLayout.addWidget(self.textEdit, 1)

            # Daha önce indirilen linkleri çıkar
            try:
                existing_links = self.data.get("links", [])
                if not isinstance(existing_links, list):
                    existing_links = []
                    self.data["links"] = []

                downloaded_items = self.data.get("downloaded", [])
                if not isinstance(downloaded_items, list):
                    downloaded_items = []
                    self.data["downloaded"] = []

                downloaded_links = set()
                for item in downloaded_items:
                    if isinstance(item, dict) and "url" in item:
                        url = item["url"]
                        if isinstance(url, str):
                            downloaded_links.add(url)

                filtered_links = []
                for link in existing_links:
                    if isinstance(link, str) and link not in downloaded_links:
                        filtered_links.append(link)
                    elif isinstance(link, dict) and "url" in link and link["url"] not in downloaded_links:
                        filtered_links.append(link["url"])

                if filtered_links:
                    self.textEdit.setPlainText("\n".join(filtered_links))
            except Exception as e:
                logging.error(f"Link ekleme penceresinde mevcut linkler işlenirken hata: {e}")
                # Hata durumunda boş bir metin kutusu göster

            self.buttonLayout = QHBoxLayout()
            self.saveBtn = QPushButton("Kaydet")
            self.closeBtn = QPushButton("Kapat")

            buttonStyle = """
                QPushButton {
                    background-color: #2B2B2B;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #FFFFFF;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                    border-color: #2196F3;
                }
            """
            self.saveBtn.setStyleSheet(buttonStyle)
            self.closeBtn.setStyleSheet(buttonStyle)

            self.saveBtn.clicked.connect(self.onSaveClicked)
            self.closeBtn.clicked.connect(self.close)

            self.buttonLayout.addStretch()
            self.buttonLayout.addWidget(self.saveBtn)
            self.buttonLayout.addWidget(self.closeBtn)
            self.buttonLayout.addStretch()

            self.mainLayout.addLayout(self.buttonLayout)

            self.setStyleSheet("""
                QDialog {
                    background-color: #2A2A2A;
                    color: #FFFFFF;
                    border: 1px solid #2196F3;
                    border-radius: 6px;
                }
            """)

            self.dragPos = None

        def mousePressEvent(self, event):
            if event.button() == Qt.LeftButton:
                self.dragPos = event.globalPos()
            super().mousePressEvent(event)

        def mouseReleaseEvent(self, event):
            self.dragPos = None
            super().mouseReleaseEvent(event)

        def mouseMoveEvent(self, event):
            if event.buttons() == Qt.LeftButton and self.dragPos is not None:
                delta = event.globalPos() - self.dragPos
                self.move(self.pos() + delta)
                self.dragPos = event.globalPos()
            super().mouseMoveEvent(event)

        def onSaveClicked(self):
            # Mevcut metin kutusundan linkleri alıp data["links"] içine koyacağız
            raw_text = self.textEdit.toPlainText().strip()
            if not raw_text:
                self.data["links"] = []
            else:
                lines = [line.strip() for line in raw_text.split("\n") if line.strip()]
                self.data["links"] = lines

            # Değişiklikleri ana profil JSON dosyasına kaydet
            if self.profile_path and os.path.exists(self.profile_path):
                try:
                    with open(self.profile_path, "w", encoding="utf-8") as f:
                        json.dump(self.data, f, indent=4, ensure_ascii=False)
                    self.changes_saved = True
                    self.statusLabel.setText("<b>PROFİL KAYDEDİLDİ!</b>")
                except Exception as e:
                    self.statusLabel.setText(f"<b>HATA:</b> {str(e)}")
                    logging.error(f"Profil dosyası kaydedilirken hata: {e}")
                    return  # Hata durumunda işlemi sonlandır
            else:
                self.statusLabel.setText("<b>KAYDEDİLDİ!</b>")
                self.changes_saved = True

            # 2 saniye sonra mesajı kaldır
            QTimer.singleShot(2000, lambda: self.statusLabel.setText(""))

            # Can barlarını güncelle
            try:
                from PyQt5.QtWidgets import QApplication
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, "update_stats"):
                        widget.update_stats()
                        break
            except Exception as e:
                logging.error(f"Can barları güncellenemedi: {e}")

            # Link eklendiğinde anında tetiklenme için indirme ve yükleme işlemini başlat
            if self.data.get("links"):
                try:
                    # Thread'e alarak UI donmasını önle
                    def threaded_process():
                        process_links_immediately()

                    import threading
                    threading.Thread(target=threaded_process, daemon=True).start()
                except Exception as e:
                    logging.error(f"Link ekleme sonrası indirme/işleme tetiklenemedi: {e}")

        def closeEvent(self, event):
            # Değişiklikler kaydedilmediyse ve link varsa kaydet
            try:
                if not self.changes_saved and self.textEdit.toPlainText().strip():
                    self.onSaveClicked()
            except Exception as e:
                logging.error(f"Link ekleme penceresi kapatılırken hata: {e}")
            super().closeEvent(event)

    def profil_sil(self):
        """
        Bu fonksiyon, mevcut profilin JSON dosyasını ve istatistiklerini tamamen siler.
        """
        try:
            # Profilin istatistik anahtarını oluşturmak için önce username ve platformu oku
            if os.path.exists(self.profilePath):
                with open(self.profilePath, "r", encoding="utf-8") as f:
                    data = json.load(f)
                username = data.get("username", "").strip()
                base_dir = os.path.dirname(os.path.dirname(self.profilePath))
                platform = os.path.basename(base_dir)

                # Profil anahtar ismini oluştur
                profile_key = f"{platform}_{username}" if username else None

                # Profil dosyasını sil
                os.remove(self.profilePath)
                logging.info(f"Profil dosyası silindi: {self.profilePath}")

                # Profilin istatistiklerini temizle
                if username and profile_key:
                    # Doğrudan ana stats_system nesnesini kullan
                    from Sorcerio1_7 import stats_system

                    from stats import stats_system

                    # İstatistik verilerini temizle
                    if profile_key in stats_system.stats_data:
                        del stats_system.stats_data[profile_key]
                    if profile_key in stats_system.last_stats_update:
                        del stats_system.last_stats_update[profile_key]
                    if profile_key in stats_system.stats_display_queue:
                        stats_system.stats_display_queue.remove(profile_key)
                    if profile_key in stats_system.profiles_to_process:
                        stats_system.profiles_to_process.discard(profile_key)

                    # Eğer silinen profil şu an gösterilen profilse, başka bir profile geç
                    if stats_system.current_displayed_profile == profile_key:
                        stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

                    # İstatistikleri dosyaya kaydet
                    stats_system.istatistikleri_kaydet()

                    logging.info(f"{profile_key} için istatistik verileri profil silme işlemi sırasında temizlendi.")

            # Silme sonrası her iki deck'i de anında ve UI thread'inde güncelle
            from PyQt5.QtCore import QTimer
            if self.parent() and hasattr(self.parent(), 'update_stats'):
                # Profil silme sonrası UI'ı hemen güncelle
                QTimer.singleShot(0, lambda: self.profile_silme_ui_guncelle(self.parent()))
                logging.info("Profil silme sonrası UI anında güncellendi.")
        except Exception as e:
            logging.error(f"Profil silinirken hata: {str(e)}")

    def profile_silme_ui_guncelle(self, parent_window):
        """
        Profil silme sonrası UI'ı güncelleyen yardımcı fonksiyon
        """
        try:
            # Ana pencerede update_stats fonksiyonunu çağır
            if hasattr(parent_window, 'update_stats'):
                parent_window.update_stats()

            # Profil listesini yeniden yükle (sol deck'i güncelle)
            parent_window.view.page().runJavaScript('''
                if (typeof refreshProfilesList === 'function') {
                    refreshProfilesList();
                }
            ''')
        except Exception as e:
            logging.error(f"Profil silme sonrası UI güncellenirken hata: {str(e)}")


class DaySelector(QWidget):
    """
    Basit bir gün seçici: 7 gün (monday..sunday) için buton dizisi.
    Sinyalle currentDay'i dışarı iletir.
    """
    dayChanged = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.currentDay = "monday"
        self.days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        self.displayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

        self.layout = QHBoxLayout(self)
        self.layout.setSpacing(5)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.buttons = []

        for i, day in enumerate(self.days):
            btn = QPushButton(self.displayNames[i])
            btn.setCheckable(True)
            btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #242424;
                    border: 1px solid #373737;
                    border-radius: 4px;
                    padding: 6px 10px;
                    color: #FFFFFF;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2196F3;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                }
                """
            )
            btn.clicked.connect(self.onDayButtonClicked)
            self.layout.addWidget(btn)
            self.buttons.append(btn)

        self.updateButtonStates()

    def setCurrentDay(self, day):
        if day in self.days:
            self.currentDay = day
        self.updateButtonStates()

    def onDayButtonClicked(self):
        sender = self.sender()
        if sender in self.buttons:
            index = self.buttons.index(sender)
            self.currentDay = self.days[index]
            self.updateButtonStates()
            self.dayChanged.emit(self.currentDay)

    def updateButtonStates(self):
        for i, day in enumerate(self.days):
            self.buttons[i].setChecked(day == self.currentDay)


class ProfileBridge(QObject):
    """
    Profil listesi oluşturma ve profil düzenleme diyalogunu açma köprüsü.
    Sol deck'te gösterilecek isimler, JSON içindeki 'username' varsa onu,
    aksi halde dosya adını gösterir.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.profileEditorDialog = None
        self.linkEditorDialog = None

    @pyqtSlot(str)
    def openLinkEditorOnly(self, profilePath):
        if getattr(self, 'linkEditorDialog', None) is not None:
            self.linkEditorDialog.close()
            self.linkEditorDialog = None
        try:
            # Profil dosyasının var olduğunu kontrol et
            if not os.path.exists(profilePath):
                logging.error(f"Profil dosyası bulunamadı: {profilePath}")
                return

            with open(profilePath, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Veri yapısını kontrol et ve gerekirse düzelt
            if not isinstance(data, dict):
                data = {}
                logging.warning(f"Profil verisi sözlük değil, düzeltiliyor: {profilePath}")

            # "links" anahtarı yoksa ekle
            if "links" not in data:
                data["links"] = []

            # "downloaded" anahtarı yoksa ekle
            if "downloaded" not in data:
                data["downloaded"] = []

            self.linkEditorDialog = ProfileEditorDialog.LinkSettingsDialog(data, self.parent())
            self.linkEditorDialog.profile_path = profilePath  # Profil yolunu ayarla
            self.linkEditorDialog.destroyed.connect(lambda: setattr(self, 'linkEditorDialog', None))
            self.linkEditorDialog.show()
        except json.JSONDecodeError as e:
            logging.error(f"Profil dosyası geçerli JSON formatında değil: {profilePath} - Hata: {e}")
        except Exception as e:
            logging.error(f"Link Ekleme Penceresi açılamadı: {e}")
            import traceback
            logging.error(traceback.format_exc())

    @pyqtSlot(str, result=str)
    def getProfiles(self, platform):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        config_dir = os.path.join(base_dir, "configuration", platform)
        profiles = []
        if os.path.exists(config_dir):
            for file in os.listdir(config_dir):
                if file.endswith(".json"):
                    path = os.path.join(config_dir, file)
                    display_name = file
                    try:
                        with open(path, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        username = data.get("username", "").strip()
                        if username:
                            display_name = username
                        else:
                            display_name = os.path.splitext(file)[0]  # dosya adını göster (örnek: profile1)
                    except:
                        pass
                    profiles.append(
                        {
                            "path": path,
                            "displayName": display_name,
                        }
                    )
        return json.dumps(profiles)

    @pyqtSlot(str)
    def openProfileEditor(self, profilePath):
        if getattr(self, 'profileEditorDialog', None) is not None:
            self.profileEditorDialog.close()
            self.profileEditorDialog = None
        self.profileEditorDialog = ProfileEditorDialog(profilePath, self.parent())
        self.profileEditorDialog.profileNameChanged.connect(self.updateProfileNameInList)
        self.profileEditorDialog.destroyed.connect(lambda: setattr(self, 'profileEditorDialog', None))
        self.profileEditorDialog.show()

    @pyqtSlot(str, str)
    def updateProfileNameInList(self, profilePath, newName):
        mainWindow = self.parent()
        if mainWindow:
            mainWindow.view.page().runJavaScript(
                f'window.updateProfileItemText({json.dumps(profilePath)}, {json.dumps(newName)});'
            )


class DestekDialog(QDialog):
    """
    Kullanıcının e-posta adresini ve mesajını alıp Telegram'a ileten destek penceresi.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setWindowModality(Qt.ApplicationModal)
        self.setModal(True)
        self.resize(400, 300)

        shadow = QGraphicsDropShadowEffect(self)
        shadow.setOffset(0, 0)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 180))
        self.setGraphicsEffect(shadow)

        self.mainLayout = QVBoxLayout(self)
        self.mainLayout.setContentsMargins(20, 20, 20, 20)
        self.mainLayout.setSpacing(15)

        self.titleLabel = QLabel("DESTEK")
        self.titleLabel.setStyleSheet("font-size: 18px; font-weight: bold; color: #2196F3; margin-bottom: 12px;")
        self.titleLabel.setAlignment(Qt.AlignCenter)
        self.mainLayout.addWidget(self.titleLabel)

        self.infoLabel = QLabel("")
        self.infoLabel.setStyleSheet("font-size: 13px; color: #39FF14; font-weight: bold;")
        self.infoLabel.setAlignment(Qt.AlignCenter)
        self.mainLayout.addWidget(self.infoLabel)

        # Email input
        self.emailLabel = QLabel("E-Posta Adresiniz:")
        self.emailLabel.setStyleSheet("font-size: 13px; font-weight: bold; color: #FFFFFF;")
        self.emailEdit = QLineEdit()
        self.emailEdit.setFixedHeight(40)
        self.emailEdit.setStyleSheet(
            "background-color: #1E1E1E; border: 1px solid #373737; border-radius: 6px;"
            "padding: 8px; color: #FFFFFF; font-size: 13px; qproperty-placeholderTextColor: #AAAAAA;"
        )
        self.emailEdit.setPlaceholderText("<EMAIL>")

        # Mesaj input
        self.messageLabel = QLabel("Mesajınız:")
        self.messageLabel.setStyleSheet("font-size: 13px; font-weight: bold; color: #FFFFFF;")
        self.messageEdit = QPlainTextEdit()
        self.messageEdit.setFixedHeight(100)
        self.messageEdit.setStyleSheet(
            "background-color: #1E1E1E; border: 1px solid #373737; border-radius: 6px;"
            "padding: 8px; color: #FFFFFF; font-size: 13px; qproperty-placeholderTextColor: #AAAAAA;"
        )
        self.messageEdit.setPlaceholderText("Lütfen mesajınızı buraya yazın...")

        formLayout = QFormLayout()
        formLayout.setSpacing(8)
        formLayout.addRow(self.emailLabel, self.emailEdit)
        formLayout.addRow(self.messageLabel, self.messageEdit)
        self.mainLayout.addLayout(formLayout)

        # Buttons
        self.buttonLayout = QHBoxLayout()
        self.sendBtn = QPushButton("Gönder")
        self.closeBtn = QPushButton("Kapat")

        btnStyle = """
            QPushButton {
                background-color: #2B2B2B;
                border: 1px solid #373737;
                border-radius: 6px;
                padding: 8px 14px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                min-width: 90px;
                transition: background 0.3s, border-color 0.3s;
            }
            QPushButton:hover {
                background-color: #2196F3;
                border-color: #2196F3;
                color: #FFFFFF;
            }
        """
        self.sendBtn.setStyleSheet(btnStyle)
        self.closeBtn.setStyleSheet(btnStyle)

        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.sendBtn)
        self.buttonLayout.addWidget(self.closeBtn)
        self.buttonLayout.addStretch()

        self.mainLayout.addLayout(self.buttonLayout)

        self.sendBtn.clicked.connect(self.sendMessage)
        self.closeBtn.clicked.connect(self.close)

        self.setStyleSheet(
            """
            QDialog {
                background-color: #2A2A2A;
                color: #FFFFFF;
                border: 2px solid #2196F3;
                border-radius: 12px;
            }
            """
        )

        self.dragPos = None

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragPos = event.globalPos()
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        self.dragPos = None
        super().mouseReleaseEvent(event)

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.dragPos is not None:
            delta = event.globalPos() - self.dragPos
            self.move(self.pos() + delta)
            self.dragPos = event.globalPos()
        super().mouseMoveEvent(event)

    def sendMessage(self):
        email = self.emailEdit.text().strip()
        message_text = self.messageEdit.toPlainText().strip()
        if not message_text:
            self.infoLabel.setText("Mesaj boş olamaz.")
            QTimer.singleShot(2000, lambda: self.infoLabel.setText(""))
            return

        full_message = f"E-Posta: {email}\nMesaj: {message_text}"
        send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, full_message)

        self.infoLabel.setText("Gönderildi!")
        QTimer.singleShot(2000, lambda: self.infoLabel.setText(""))
        # İsterseniz formu temizleyebilirsiniz:
        self.emailEdit.clear()
        self.messageEdit.clear()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)

        # Uygulamanın temel ve minimum boyutları (DPI duyarlı)
        BASE_APP_WIDTH = 900.0
        BASE_APP_HEIGHT = 600.0
        ASPECT_RATIO = BASE_APP_HEIGHT / BASE_APP_WIDTH

        MIN_PREFERRED_WIDTH = 800.0
        MIN_PREFERRED_HEIGHT = MIN_PREFERRED_WIDTH * ASPECT_RATIO

        # Maksimum boyutlar - çok büyük ekranlarda aşırı büyümeyi önler
        MAX_APP_WIDTH = 1600.0
        MAX_APP_HEIGHT = MAX_APP_WIDTH * ASPECT_RATIO

        current_screen_geometry = None
        primary_screen = QApplication.primaryScreen()
        if primary_screen:
            current_screen_geometry = primary_screen.availableGeometry()

        if current_screen_geometry:
            available_width = float(current_screen_geometry.width())
            available_height = float(current_screen_geometry.height())

            target_width = BASE_APP_WIDTH
            target_height = BASE_APP_HEIGHT

            # Eğer temel boyutlar ekrana sığmıyorsa, sığacak şekilde ölçekle
            if target_width > available_width or target_height > available_height:
                scale_factor_to_fit = min(available_width / target_width, available_height / target_height)
                target_width = target_width * scale_factor_to_fit
                target_height = target_height * scale_factor_to_fit

            # Maksimum boyutları kontrol et - çok büyük ekranlarda aşırı büyümeyi önle
            if target_width > MAX_APP_WIDTH:
                target_width = MAX_APP_WIDTH
                target_height = MAX_APP_HEIGHT

            # Nihai boyutları belirle (target_width/height zaten ekrana sığıyor)
            final_width = target_width
            final_height = target_height

            # Eğer ölçeklenmiş boyutlar tercih edilen minimumdan küçükse
            # ve tercih edilen minimum ekrana sığıyorsa, minimumu kullan.
            if (final_width < MIN_PREFERRED_WIDTH or final_height < MIN_PREFERRED_HEIGHT):
                if MIN_PREFERRED_WIDTH <= available_width and MIN_PREFERRED_HEIGHT <= available_height:
                    final_width = MIN_PREFERRED_WIDTH
                    final_height = MIN_PREFERRED_HEIGHT
                # Else: Tercih edilen minimum ekrana sığmıyor, bu yüzden zaten ekrana sığan
                # `target_width` ve `target_height` değerleri kullanılacak (yukarıda final_width/height olarak atandı).

            self.resize(int(final_width), int(final_height))

        else:
            # Ekran bilgisi alınamazsa orijinal sabit boyuta geri dön
            self.resize(int(BASE_APP_WIDTH), int(BASE_APP_HEIGHT))

        self.setWindowTitle("3 Deck UI - Dark Theme (Modernized)")
        self.view = QWebEngineView()

        # DPI duyarlı zoom faktörü ayarla
        try:
            primary_screen = QApplication.primaryScreen()
            if primary_screen:
                device_pixel_ratio = primary_screen.devicePixelRatio()
                # Zoom faktörünü cihaz piksel oranına göre ayarla
                self.view.setZoomFactor(device_pixel_ratio)
                logging.info(f"DPI zoom faktörü ayarlandı: {device_pixel_ratio}")
        except Exception as e:
            logging.warning(f"DPI zoom faktörü ayarlanırken hata: {e}")
            # Varsayılan zoom faktörü (1.0) kullanılacak

        self.setCentralWidget(self.view)
        self.channel = QWebChannel(self.view.page())
        self.bridge = JsBridge(self)
        self.profileBridge = ProfileBridge(self)
        self.channel.registerObject("pyBridge", self.bridge)
        self.channel.registerObject("profileBridge", self.profileBridge)
        self.view.page().setWebChannel(self.channel)
        self.view.setHtml(html_content)
        self.view.loadFinished.connect(self.on_load_finished)
        self.centerWindow()

        # İstatistik sistemi için timer başlat
        self.stats_timer = QTimer(self)
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(5000)  # 5 saniyede bir kontrol et (daha sık güncelleme)

        # Profil senkronizasyonu timer'ı
        self.sync_timer = QTimer(self)
        self.sync_timer.timeout.connect(self.sync_profiles_with_json)
        self.sync_timer.start(30000)  # 30 saniyede bir profil verilerini senkronize et

        # İstatistik sistemini başlat
        initialize_stats_system()
        # İstatistikler yüklenir yüklenmez anında göster
        self.update_stats()

        # Uygulama başladığında profil senkronizasyonunu çalıştır
        QTimer.singleShot(2000, self.sync_profiles_with_json)

        # Live Feed bağlantısını kur
        live_feed_manager.feed_updated.connect(self.update_live_feed)

        # Clean up old thumbnails on startup
        live_feed_manager.cleanup_old_thumbnails()

        # Load existing events or show skeleton
        if live_feed_manager.is_empty():
            self.show_live_feed_skeleton()
        else:
            # Trigger update with loaded events
            self.update_live_feed(live_feed_manager.get_events())

        # 0.5 saniye sonra durumu 'stopped' olarak ayarla (başlangıç animasyonunu bastırmak için)
        QTimer.singleShot(500, lambda: self.set_status_bar_color(False))
        self.set_status_bar_color(False)  # Garantili kırmızı başlat

    def sync_profiles_with_json(self):
        """
        Arayüzdeki profil verileri ile JSON dosyalarını senkronize eder.
        1. Geçersiz profilleri (kullanıcı adı/şifresi silinmiş fakat içeriği duran) temizler
        2. Silinen profillere ait JSON dosyalarındaki içeriği temizler
        3. Arayüzdeki profil listesini günceller
        """
        try:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            logging.info("Profil-JSON senkronizasyonu başlatılıyor...")

            # Tüm platformlar için işlem yap
            for platform in ["instagram", "twitter"]:
                config_dir = os.path.join(base_dir, "configuration", platform)
                if not os.path.exists(config_dir):
                    continue

                # Tüm profil JSON dosyalarını tara
                for profile_file in os.listdir(config_dir):
                    if not profile_file.endswith(".json"):
                        continue

                    profile_path = os.path.join(config_dir, profile_file)
                    try:
                        with open(profile_path, "r", encoding="utf-8") as f:
                            data = json.load(f)

                        # Kullanıcı adı ve şifre var mı kontrol et
                        username = data.get("username", "").strip()
                        password = data.get("password", "").strip()

                        # Eğer kullanıcı adı veya şifre boşsa fakat içerik varsa (linkler, downloaded vb)
                        has_content = (len(data.get("links", [])) > 0 or
                                      len(data.get("downloaded", [])) > 0 or
                                      any(len(day_times) > 0 for day_times in data.get("schedule", {}).values()))

                        if (not username or not password) and has_content:
                            # Profil içeriğini sıfırla
                            clean_data = {
                                "username": username,
                                "password": password,
                                "schedule": {
                                    "monday": [],
                                    "tuesday": [],
                                    "wednesday": [],
                                    "thursday": [],
                                    "friday": [],
                                    "saturday": [],
                                    "sunday": [],
                                },
                                "hashtags": [],
                                "links": [],
                                "downloaded": []
                            }

                            # JSON dosyasını güncelle
                            with open(profile_path, "w", encoding="utf-8") as f:
                                json.dump(clean_data, f, indent=4)

                            logging.info(f"Kullanıcı bilgisi eksik profil temizlendi: {profile_path}")

                    except Exception as e:
                        logging.error(f"Profil dosyası kontrol edilirken hata: {profile_path} - {e}")

            # İstatistikleri ve arayüzü güncelle
            self.update_stats()

            # Sol deck'teki profil listesini yenile
            self.view.page().runJavaScript('''
                if (typeof refreshProfilesList === 'function') {
                    refreshProfilesList();
                } else {
                    let platformSelect = document.getElementById("platformSelect");
                    if (platformSelect) {
                        let selectedPlatform = platformSelect.value;
                        profileBridge.getProfiles(selectedPlatform, function(response) {
                            updateProfileList(response);
                        });
                    }
                }
            ''')

            logging.info("Profil-JSON senkronizasyonu tamamlandı.")
        except Exception as e:
            logging.error(f"Profil senkronizasyonu sırasında genel hata: {e}")

    def on_load_finished(self):
        self.set_status_bar_color(False)

        # JavaScript tarafından erişim için mainWindow değişkenini set et
        self.view.page().runJavaScript("window.mainWindow = null;")  # Önce temizle
        self.view.page().runJavaScript("window.mainWindow = window.pyBridge.mainWindow;")

        # Test events removed - no longer needed

    def set_status_bar_color(self, is_working):
        if is_working is None:
            state = "stopped"  # Başlangıçta da kırmızı yap
        else:
            state = "working" if is_working else "stopped"
        js = f'setStatusBarColor("{state}");'
        self.view.page().runJavaScript(js)

    def centerWindow(self):
        frame_geom = self.frameGeometry()
        screen_center = QDesktopWidget().availableGeometry().center()
        frame_geom.moveCenter(screen_center)
        self.move(frame_geom.topLeft())

    def update_stats(self):
        try:
            # Profilleri kontrol et ve yeni HTML oluştur (Can Barları için - extra-deck)
            check_profiles(force=True)
            html_can_barlari = update_stats_display_html()
            # Skeleton animasyonunu kaldırıp can barlarını güncelle (extra-deck)
            self.view.page().runJavaScript(f'''
                (function() {{
                    const extraDeck = document.querySelector(".extra-deck");
                    if (!extraDeck) return;
                    const newHtml = {json.dumps(html_can_barlari)};
                    // extraDeck'in içeriğini tamamen yeni HTML ile değiştir
                    extraDeck.innerHTML = newHtml;
                }})();
            ''')

            # Var olan profil anahtarlarını bulmak için mevcut JSON dosyalarını kontrol et
            base_dir = os.path.dirname(os.path.abspath(__file__))
            mevcut_profiller = set()
            for platform in ["instagram", "twitter"]:
                platform_dir = os.path.join(base_dir, "configuration", platform)
                if not os.path.exists(platform_dir):
                    continue
                for file in os.listdir(platform_dir):
                    if file.endswith(".json"):
                        try:
                            with open(os.path.join(platform_dir, file), "r", encoding="utf-8") as f:
                                data = json.load(f)
                                username = data.get("username", "").strip()
                                if username:
                                    profile_key = f"{platform}_{username}"
                                    mevcut_profiller.add(profile_key)
                        except Exception as e:
                            logging.error(f"Profil dosyası okuma hatası {file}: {str(e)}")
                            pass

            # stats_system verilerini ve stats_display_queue'yu temizle
            # Sadece mevcut_profiller'de olan VE stats_data'da verisi olanları tut

            # 1. stats_data'yı temizle
            silinecek_keys_stats_data = [
                key for key in list(stats_system.stats_data.keys()) if key not in mevcut_profiller
            ]
            updated_during_cleanup = False
            for key in silinecek_keys_stats_data:
                del stats_system.stats_data[key]
                if key in stats_system.last_stats_update:
                    del stats_system.last_stats_update[key]
                if key in stats_system.profiles_to_process:
                    stats_system.profiles_to_process.discard(key)
                updated_during_cleanup = True
                logging.info(f"UI güncellemesi sırasında var olmayan profile ait istatistik (data) temizlendi: {key}")

            # 2. stats_display_queue'yu yeniden oluştur
            old_queue_len = len(stats_system.stats_display_queue)
            new_display_queue = deque()
            processed_in_queue = set()  # Kuyrukta mükerrerliği önlemek için
            for key_in_q in list(stats_system.stats_display_queue):  # Orijinal kuyruğun kopyası üzerinde iterasyon
                if key_in_q in mevcut_profiller and key_in_q in stats_system.stats_data and key_in_q not in processed_in_queue:
                    new_display_queue.append(key_in_q)
                    processed_in_queue.add(key_in_q)
            stats_system.stats_display_queue = new_display_queue
            if len(stats_system.stats_display_queue) != old_queue_len:
                updated_during_cleanup = True

            if updated_during_cleanup:
                stats_system.istatistikleri_kaydet()  # Değişiklik olduysa dosyayı kaydet

            # current_displayed_profile'ın hala geçerli olup olmadığını kontrol et
            if stats_system.current_displayed_profile and \
                    (stats_system.current_displayed_profile not in mevcut_profiller or \
                     stats_system.current_displayed_profile not in stats_system.stats_data):
                stats_system.current_displayed_profile = next(iter(stats_system.stats_display_queue), None)

            # --- Sağ deck'teki (istatistikler) detaylı gösterim mantığı ---
            if not stats_system.stats_display_queue:
                # Gösterilecek geçerli ve verisi olan profil yoksa, iskelet HTML'ini sağ deck'e bas
                self.set_right_deck_to_skeleton_html()
            else:
                # Profil değişim zamanlaması kontrolü (eski implementasyondan)
                now = datetime.now()
                time_since_last_change = (now - stats_system.last_display_change).total_seconds()

                # Eğer 10 saniyeden fazla geçmişse veya current_displayed_profile yoksa, profil değiştir
                should_switch_profile = (
                    time_since_last_change >= 10 or
                    stats_system.current_displayed_profile is None or
                    stats_system.current_displayed_profile not in stats_system.stats_display_queue
                )

                if should_switch_profile and len(stats_system.stats_display_queue) > 1:
                    # Profil rotasyonu yap
                    profile_key_to_display = stats_system.stats_display_queue.popleft()
                    stats_system.stats_display_queue.append(profile_key_to_display)  # Rotasyon için sona ekle
                    stats_system.current_displayed_profile = profile_key_to_display  # Takip için güncelle
                    stats_system.last_display_change = now  # Son değişim zamanını güncelle
                    logging.info(f"Profil rotasyonu: {profile_key_to_display} gösteriliyor")
                elif stats_system.current_displayed_profile in stats_system.stats_display_queue:
                    # Mevcut profili göstermeye devam et
                    profile_key_to_display = stats_system.current_displayed_profile
                else:
                    # Fallback: İlk profili göster
                    profile_key_to_display = stats_system.stats_display_queue[0] if stats_system.stats_display_queue else None
                    stats_system.current_displayed_profile = profile_key_to_display
                    stats_system.last_display_change = now

                if profile_key_to_display:
                    stats_data = stats_system.stats_data.get(profile_key_to_display)

                    right_deck_content_html = ""  # .right-deck'in tamamı için HTML

                    if stats_data:
                        platform = stats_data.get("platform")
                        stats_specific_html = ""  # Sadece ilgili platformun istatistik HTML'i

                        if platform == "twitter":
                            stats_specific_html = format_twitter_stats_as_boxes(stats_data)
                            # .right-deck için tam HTML: Twitter stats + boş Instagram stats div
                            right_deck_content_html = f'''
                                <div class="stats-grid twitter-stats">{stats_specific_html}</div>
                                <div class="stats-grid instagram-stats"></div>
                            '''
                        elif platform == "instagram":
                            stats_specific_html = format_instagram_stats_as_two_columns(stats_data)
                            # .right-deck için tam HTML: Boş Twitter stats div + Instagram stats
                            right_deck_content_html = f'''
                                <div class="stats-grid twitter-stats"></div>
                                <div class="stats-grid instagram-stats">{stats_specific_html}</div>
                            '''

                        if right_deck_content_html:
                            self.view.page().runJavaScript(f'''
                                var rightDeck = document.querySelector(".right-deck");
                                if (rightDeck) {{
                                    rightDeck.innerHTML = {json.dumps(right_deck_content_html)};
                                }}
                            ''')
                        else:
                            # Bu durum stats_data geçerli ama platform bilinmiyorsa olabilir (normalde olmamalı)
                            logging.warning(
                                f"Geçerli platform için HTML oluşturulamadı: {platform}. İskelet gösteriliyor.");
                            self.set_right_deck_to_skeleton_html()
                    else:
                        # stats_data beklenmedik şekilde null ise (kuyruk temizliğine rağmen).
                        logging.warning(
                            f"İstatistik verisi {profile_key_to_display} için bulunamadı (beklenmedik). İskelet gösteriliyor.");
                        self.set_right_deck_to_skeleton_html()
                else:
                    self.set_right_deck_to_skeleton_html()
        except Exception as e:
            logging.error(f"İstatistik güncellenirken genel hata oluştu: {str(e)}")
            # Genel bir hata durumunda da sağ deck'i iskelet moduna almak güvenlidir.
            self.set_right_deck_to_skeleton_html()

    def set_right_deck_to_skeleton_html(self):
        """
        Sağ deck'in (istatistikler) içeriğini sadece iskelet animasyonu ile doldurur.
        """
        skeleton_html_for_right_deck = '''
        <div class="skeleton-container" id="right-skeleton">
            <div class="skeleton" style="width: 85%; height: 45px;"></div>
            <div class="skeleton" style="width: 80%; height: 45px;"></div>
            <div class="skeleton" style="width: 75%; height: 45px;"></div>
        </div>
        '''
        try:
            self.view.page().runJavaScript(f'''
                var rightDeck = document.querySelector(".right-deck");
                if (rightDeck) {{
                    rightDeck.innerHTML = {json.dumps(skeleton_html_for_right_deck)};
                }}
            ''')
        except Exception as e:
            logging.error(f"Sağ deck iskelet HTML'i ayarlanırken hata: {e}")

    @pyqtSlot()
    def startProcessing(self):
        """
        'Başla' butonuna basıldığında:
          1) Tüm profillerdeki linkleri indirir
          2) Her profildeki schedule saatine göre paylaşım görevlerini planlar
          3) Scheduler başlatılır
        """
        # 1) Tüm profillerdeki linkleri hemen indir
        threading.Thread(target=download_all_profiles_links, daemon=True).start()

        # 2) Scheduler oluştur ve job'ları tanımla
        reload_scheduler_helper(self)

    @pyqtSlot()
    def stopProcessing(self):
        # Scheduler'ı durdur
        if hasattr(self, 'scheduler'):
            try:
                self.scheduler.shutdown(wait=False)
                logging.info("Scheduler durduruldu.")
            except Exception as e:
                logging.error(f"Scheduler durdurulurken hata: {e}")
        # UI durumunu 'stopped' olarak güncelle ve log mesajı yaz
        self.set_status_bar_color(False)
        logging.info("İşlem durduruldu.")

    def reload_scheduler(self):
        reload_scheduler_helper(self)

    def update_live_feed(self, events):
        """Live Feed'i güncelleyen fonksiyon"""
        try:
            if not events:
                # Eğer event yoksa skeleton göster
                self.show_live_feed_skeleton()
                return

            # Event'leri HTML'e çevir
            feed_html = ""
            for event in events:
                event_html = format_live_feed_event(event)
                if event_html:
                    feed_html += event_html

            # JavaScript ile Live Feed'i güncelle
            self.view.page().runJavaScript(f'''
                (function() {{
                    const container = document.getElementById("live-feed-container");
                    if (container) {{
                        container.innerHTML = {json.dumps(feed_html)};
                    }}
                }})();
            ''')

        except Exception as e:
            logging.error(f"Live Feed güncellenirken hata: {e}")

    def show_live_feed_skeleton(self):
        """Live Feed skeleton animasyonunu gösterir"""
        try:
            skeleton_html = ""
            for _ in range(3):  # 3 skeleton item
                skeleton_html += get_skeleton_html()

            # JavaScript ile skeleton'ı göster
            self.view.page().runJavaScript(f'''
                (function() {{
                    const container = document.getElementById("live-feed-container");
                    if (container) {{
                        container.innerHTML = {json.dumps(skeleton_html)};
                    }}
                }})();
            ''')

        except Exception as e:
            logging.error(f"Live Feed skeleton gösterilirken hata: {e}")




# QApplication artık main.py'de oluşturuluyor

def process_links_immediately():
    """
    Anında indirme ve işleme döngüsünü tetikler.
    Bu fonksiyon hem indirme (download_all_profiles_links) hem de
    işleme/yükleme (upload_all_profiles_links) fonksiyonlarını çağırır.
    """
    from threading import Thread
    from download import download_all_profiles_links
    from upload import upload_all_profiles_links

    def process_thread():
        try:
            # 1. Adım: Tüm profillerdeki linkleri indir
            download_all_profiles_links()

            # 2. Adım: İndirilen linkleri işle/yükle
            upload_all_profiles_links()

            logging.info("Anında indirme ve işleme döngüsü tamamlandı")
        except Exception as e:
            logging.error(f"Anında indirme ve işleme döngüsünde hata: {e}")
            import traceback
            logging.error(traceback.format_exc())

    # Arka planda işlem başlat
    try:
        processing_thread = Thread(target=process_thread, daemon=True)
        processing_thread.start()
        logging.info("İndirme ve işleme thread'i başlatıldı")
    except Exception as e:
        logging.error(f"İndirme ve işleme thread'i başlatılamadı: {e}")
        import traceback
        logging.error(traceback.format_exc())
