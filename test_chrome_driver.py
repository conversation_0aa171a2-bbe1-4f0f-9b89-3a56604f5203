#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Chrome Driver Test Script
Bu script Chrome driver'ın düzgün çalışıp çalışmadığını test eder.
"""

import logging
import sys
from utils import create_chrome_driver_for_account, setup_logging

def test_chrome_driver():
    """Chrome driver test fonksiyonu"""
    
    # Logging'i ayarla
    setup_logging()
    
    print("Chrome Driver Test Başlatılıyor...")
    print("=" * 50)
    
    try:
        # Test hesabı için driver oluştur
        test_username = "test_account"
        print(f"Test hesabı için Chrome driver oluşturuluyor: {test_username}")
        
        driver = create_chrome_driver_for_account(test_username, headless=True)
        
        if driver:
            print("✅ Chrome driver başarıyla oluşturuldu!")
            
            # Basit bir test yap
            print("Google'a gidiliyor...")
            driver.get("https://www.google.com")
            
            title = driver.title
            print(f"<PERSON><PERSON> başlığı: {title}")
            
            if "Google" in title:
                print("✅ Chrome driver düzgün çalışıyor!")
            else:
                print("❌ Chrome driver çalışıyor ama beklenmeyen sonuç!")
            
            # Driver'ı kapat
            driver.quit()
            print("✅ Chrome driver başarıyla kapatıldı!")
            
        else:
            print("❌ Chrome driver oluşturulamadı!")
            return False
            
    except Exception as e:
        print(f"❌ Chrome driver test hatası: {e}")
        logging.error(f"Chrome driver test hatası: {e}")
        return False
    
    print("=" * 50)
    print("Chrome Driver Test Tamamlandı!")
    return True

if __name__ == "__main__":
    success = test_chrome_driver()
    sys.exit(0 if success else 1) 