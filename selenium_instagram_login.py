import json, os, time
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging
from utils import create_chrome_driver_for_account

def get_sessions_dir():
    base_dir = Path(__file__).resolve().parent
    sess_dir = base_dir / "configuration" / "instagram" / "sessions"
    sess_dir.mkdir(parents=True, exist_ok=True)
    return sess_dir

def create_instagram_driver(username):
    """
    Instagram için Chrome driver oluşturur.
    utils.py'daki optimize edilmiş Chrome driver yönet<PERSON><PERSON> kullanır.
    """
    try:
        driver = create_chrome_driver_for_account(username, headless=True)
        return driver
    except Exception as e:
        logging.error(f"Instagram Chrome driver oluşturulamadı {username}: {e}")
        raise e

def login_and_get_sessionid(username: str, password: str, headless=False) -> str:
    """
    Selenium ile instagram.com'a girer, 'sessionid' çerezini döner.
    Çerez json'u `sessions/{username}_cookies.json` olarak kaydedilir.
    Ayrıca instagrapi session dosyasını da günceller.

    Geliştirilmiş sürüm:
    - Daha uzun bekleme süreleri
    - İnsan benzeri gecikmeler
    - Daha güvenilir oturum kontrolü
    - Oturum açma sonrası doğrulama
    - Checkpoint doğrulama desteği
    - Instagrapi session dosyasını da günceller
    """
    logging.info(f"Instagram oturumu açılıyor: {username}")
    
    # utils.py'daki optimize edilmiş Chrome driver yönetimini kullan
    try:
        driver = create_instagram_driver(username)
    except Exception as e:
        logging.error(f"Chrome driver oluşturulamadı: {e}")
        raise e
        
    try:
        # Instagram'a git
        driver.get("https://www.instagram.com/")
        time.sleep(3)  # Sayfa yüklensin

        # Login sayfasına git
        driver.get("https://www.instagram.com/accounts/login/")

        # Kullanıcı adı ve şifre alanlarını bekle
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.NAME, "username"))
        )

        # İnsan benzeri gecikme
        time.sleep(1.5)

        # Kullanıcı adını yavaşça gir
        username_field = driver.find_element(By.NAME, "username")
        for char in username:
            username_field.send_keys(char)
            time.sleep(0.1)

        time.sleep(0.8)

        # Şifreyi yavaşça gir
        password_field = driver.find_element(By.NAME, "password")
        for char in password:
            password_field.send_keys(char)
            time.sleep(0.1)

        time.sleep(1)

        # Giriş butonuna tıkla
        driver.find_element(By.XPATH, "//button[@type='submit']").click()

        # Checkpoint kontrolü - 2FA veya güvenlik doğrulaması gerekebilir
        time.sleep(5)  # Sayfanın yüklenmesi için bekle

        # Checkpoint sayfasında mıyız kontrol et
        if "challenge" in driver.current_url or "checkpoint" in driver.current_url:
            logging.warning(f"Instagram güvenlik doğrulaması gerekiyor: {username}")

            # Kullanıcıya bilgi ver ve manuel işlem için bekle
            print("\n" + "="*50)
            print(f"DİKKAT: Instagram hesabı '{username}' için güvenlik doğrulaması gerekiyor.")
            print("Tarayıcı penceresinde doğrulama işlemini tamamlayın.")
            print("İşlem tamamlandıktan sonra Enter tuşuna basın...")
            print("="*50 + "\n")

            # Kullanıcı Enter tuşuna basana kadar bekle
            input("Doğrulama işlemini tamamladıktan sonra Enter tuşuna basın...")

            # Doğrulama sonrası ana sayfaya yönlendirilmiş olmalıyız
            time.sleep(5)

            # Hala checkpoint sayfasındaysak hata ver
            if "challenge" in driver.current_url or "checkpoint" in driver.current_url:
                logging.error("Doğrulama işlemi tamamlanamadı!")
                raise RuntimeError("Instagram güvenlik doğrulaması tamamlanamadı!")

        # Giriş sonrası navigasyon menüsünü bekle
        try:
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//nav"))
            )
        except Exception as e:
            logging.warning(f"Navigasyon menüsü bulunamadı, alternatif kontrol yapılıyor: {e}")
            # Alternatif kontrol: Ana sayfaya git ve oturumun açık olduğunu doğrula
            driver.get("https://www.instagram.com/")
            time.sleep(5)

        # Ana sayfaya git ve oturumun açık olduğunu doğrula
        driver.get("https://www.instagram.com/")
        time.sleep(3)

        # Çerezleri al
        cookies = {c["name"]: c["value"] for c in driver.get_cookies()}

        # sessionid kontrolü
        if "sessionid" not in cookies or not cookies["sessionid"]:
            logging.error("Instagram sessionid çerezi alınamadı!")
            raise RuntimeError("sessionid çerezi alınamadı veya boş!")

        logging.info(f"Instagram sessionid başarıyla alındı: {username}")

        # Çerezleri kaydet
        sess_dir = get_sessions_dir()
        cookie_file = sess_dir / f"{username}_cookies.json"
        with open(cookie_file, "w", encoding="utf-8") as f:
            json.dump(cookies, f, indent=2)

        # Instagrapi session dosyasını da güncelle
        instagrapi_session_file = sess_dir / f"{username}_session.json"
        try:
            session_data = {}
            if instagrapi_session_file.is_file():
                try:
                    with open(instagrapi_session_file, "r", encoding="utf-8") as f:
                        session_data = json.load(f)
                except Exception:
                    session_data = {}

            # Temel session yapısını oluştur
            if not session_data:
                import uuid
                from datetime import datetime
                session_data = {
                    "uuids": {
                        "phone_id": str(uuid.uuid4()),
                        "uuid": str(uuid.uuid4()),
                        "client_session_id": str(uuid.uuid4()),
                        "advertising_id": str(uuid.uuid4()),
                        "android_device_id": f"android-{uuid.uuid4().hex[:16]}",
                        "request_id": str(uuid.uuid4()),
                        "tray_session_id": str(uuid.uuid4())
                    },
                    "mid": cookies.get("mid", ""),
                    "ig_u_rur": None,
                    "ig_www_claim": None,
                    "authorization_data": {
                        "ds_user_id": cookies.get("ds_user_id", ""),
                        "sessionid": cookies["sessionid"]
                    },
                    "cookies": {},
                    "last_login": datetime.now().timestamp(),
                    "device_settings": {
                        "app_version": "269.0.0.18.75",
                        "android_version": 26,
                        "android_release": "8.0.0",
                        "dpi": "480dpi",
                        "resolution": "1080x1920",
                        "manufacturer": "OnePlus",
                        "device": "devitron",
                        "model": "6T Dev",
                        "cpu": "qcom",
                        "version_code": "314665256"
                    },
                    "user_agent": "Instagram 269.0.0.18.75 Android (26/8.0.0; 480dpi; 1080x1920; OnePlus; 6T Dev; devitron; qcom; en_US; 314665256)",
                    "country": "US",
                    "country_code": 1,
                    "locale": "en_US",
                    "timezone_offset": -14400
                }
            else:
                # Sadece authorization_data'yı güncelle
                if "authorization_data" not in session_data:
                    session_data["authorization_data"] = {}

                session_data["authorization_data"]["sessionid"] = cookies["sessionid"]
                if "ds_user_id" in cookies:
                    session_data["authorization_data"]["ds_user_id"] = cookies["ds_user_id"]

                session_data["last_login"] = datetime.now().timestamp()

            # Instagrapi session dosyasını kaydet
            with open(instagrapi_session_file, "w", encoding="utf-8") as f:
                json.dump(session_data, f, indent=4)

            logging.info(f"Instagrapi session dosyası güncellendi: {username}")

            # Instaloader session dosyasını da güncelle (pickle formatında)
            try:
                import instaloader
                L = instaloader.Instaloader()
                L.load_session("", cookies)
                instaloader_session_file = sess_dir / f"session-{username}.json"
                L.save_session_to_file(str(instaloader_session_file))
                logging.info(f"Instaloader session dosyası güncellendi: {username}")

                # Meta dosyasını da güncelle
                meta_file = sess_dir / f"session-{username}.meta.json"
                meta_data = {
                    "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "last_used": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                with open(meta_file, "w", encoding="utf-8") as f:
                    json.dump(meta_data, f, indent=4)
            except Exception as e:
                logging.warning(f"Instaloader session dosyası güncellenirken hata: {e}")

        except Exception as e:
            logging.warning(f"Instagrapi session dosyası güncellenirken hata: {e}")

        return cookies["sessionid"]
    except Exception as e:
        logging.error(f"Instagram oturum açma hatası: {e}")
        raise
    finally:
        driver.quit()

def load_or_login_sessionid(username: str, password: str) -> str:
    """
    Önce kaydedilmiş çerezi dener, yoksa Selenium login yapar.

    Geliştirilmiş sürüm:
    - sessionid'nin boş olup olmadığını kontrol eder
    - Çerez dosyasının yaşını kontrol eder (12 saatten eskiyse yeniler)
    - Hata durumunda daha detaylı loglama yapar
    - Her zaman non-headless mod kullanır (checkpoint doğrulama için)
    - Instagrapi session dosyasını da kontrol eder ve gerekirse senkronize eder
    """
    import logging
    from datetime import datetime, timedelta

    sess_dir = get_sessions_dir()
    cookie_file = sess_dir / f"{username}_cookies.json"
    instagrapi_session_file = sess_dir / f"{username}_session.json"

    # 1. Önce cookie dosyasını kontrol et
    if cookie_file.is_file():
        try:
            # Dosya yaşını kontrol et (12 saate düşürüldü)
            file_age = datetime.now() - datetime.fromtimestamp(cookie_file.stat().st_mtime)
            if file_age > timedelta(hours=12):
                logging.info(f"Çerez dosyası 12 saatten eski, yenileniyor: {username}")
                return login_and_get_sessionid(username, password, headless=False)

            # Çerezleri yükle
            cookies = json.loads(cookie_file.read_text(encoding="utf-8"))

            # sessionid var mı ve boş değil mi kontrol et
            if "sessionid" in cookies and cookies["sessionid"]:
                logging.info(f"Mevcut çerez dosyası kullanılıyor: {username} (yaş: {file_age.total_seconds()/3600:.1f} saat)")

                # Eğer instagrapi session dosyası varsa, cookie'deki sessionid'yi oraya da yaz
                if instagrapi_session_file.is_file():
                    try:
                        with open(instagrapi_session_file, "r", encoding="utf-8") as f:
                            session_data = json.load(f)

                        # Sessionid'yi güncelle
                        if "authorization_data" not in session_data:
                            session_data["authorization_data"] = {}

                        if not session_data.get("authorization_data", {}).get("sessionid") or \
                           session_data["authorization_data"]["sessionid"] != cookies["sessionid"]:
                            session_data["authorization_data"]["sessionid"] = cookies["sessionid"]
                            session_data["authorization_data"]["ds_user_id"] = cookies.get("ds_user_id", "")

                            # Güncellenmiş session dosyasını kaydet
                            with open(instagrapi_session_file, "w", encoding="utf-8") as f:
                                json.dump(session_data, f, indent=4)
                            logging.info(f"Instagrapi session dosyası cookie ile güncellendi: {username}")
                    except Exception as e:
                        logging.warning(f"Instagrapi session dosyası güncellenirken hata: {e}")

                return cookies["sessionid"]
            else:
                # Cookie'de sessionid boş, instagrapi session dosyasını kontrol et
                if instagrapi_session_file.is_file():
                    try:
                        with open(instagrapi_session_file, "r", encoding="utf-8") as f:
                            session_data = json.load(f)

                        # Instagrapi session dosyasından sessionid'yi al
                        if session_data.get("authorization_data", {}).get("sessionid"):
                            sessionid = session_data["authorization_data"]["sessionid"]
                            logging.info(f"Instagrapi session dosyasından sessionid alındı: {username}")

                            # Cookie dosyasını güncelle
                            cookies["sessionid"] = sessionid
                            with open(cookie_file, "w", encoding="utf-8") as f:
                                json.dump(cookies, f, indent=2)
                            logging.info(f"Cookie dosyası instagrapi session ile güncellendi: {username}")

                            return sessionid
                    except Exception as e:
                        logging.warning(f"Instagrapi session dosyası okunurken hata: {e}")

                logging.warning(f"Çerez dosyasında sessionid boş, yenileniyor: {username}")
                return login_and_get_sessionid(username, password, headless=False)

        except Exception as e:
            logging.error(f"Çerez dosyası okuma hatası ({username}): {e}")
            try:
                cookie_file.unlink(missing_ok=True)
                logging.info(f"Bozuk çerez dosyası silindi: {username}")
            except Exception as e2:
                logging.error(f"Çerez dosyası silinemedi: {e2}")

    # 2. Cookie dosyası yoksa, instagrapi session dosyasını kontrol et
    elif instagrapi_session_file.is_file():
        try:
            with open(instagrapi_session_file, "r", encoding="utf-8") as f:
                session_data = json.load(f)

            # Instagrapi session dosyasından sessionid'yi al
            if session_data.get("authorization_data", {}).get("sessionid"):
                sessionid = session_data["authorization_data"]["sessionid"]
                logging.info(f"Instagrapi session dosyasından sessionid alındı: {username}")

                # Cookie dosyasını oluştur
                cookies_dict = {
                    "sessionid": sessionid,
                    "ds_user_id": session_data.get("authorization_data", {}).get("ds_user_id", ""),
                    "csrftoken": "",
                    "ig_did": "",
                    "mid": session_data.get("mid", ""),
                    "ig_cb": "1",
                    "ig_pr": "1",
                    "ig_vw": "1920",
                    "s_network": ""
                }

                with open(cookie_file, "w", encoding="utf-8") as f:
                    json.dump(cookies_dict, f, indent=2)
                logging.info(f"Cookie dosyası instagrapi session'dan oluşturuldu: {username}")

                return sessionid
        except Exception as e:
            logging.warning(f"Instagrapi session dosyası okunurken hata: {e}")

    # 3. Hiçbir dosya yoksa veya hata durumunda yeni oturum aç
    logging.info(f"Yeni Instagram oturumu açılıyor: {username}")
    return login_and_get_sessionid(username, password, headless=False)