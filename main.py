#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Tam entegre edilmiş program:
- Video indirme, yükleme, konfigürasyon işlemleri
- PyQt5 tabanlı modern arayüz (3 deck UI)
- <PERSON>il seçim<PERSON> ve düzenlen<PERSON>i (pop-up pencere)
- "Saat Ayarları" (gün bazlı)
- "Link Ekle" (gün bazsız, tek listede link tutma)
- Link ekleme penceresi tekrar açıldığında, henüz indirilmeyen linkler JSON'dan okunup görüntülenir.
- Artık instagram_links.txt / youtube_links.txt / twitter_links.txt DOSYALARI KULLANILMAZ,
  indirme kaynağı olarak ilgili profilin .json içindeki "links" listesi kullanılır.
"""

import sys
import os
import logging
from pathlib import Path

# Selenium ve undetected_chromedriver importları (Twitter upload için)

# Instagrapi (Instagram API için)

from PyQt5.QtCore import (
    QCoreApplication, Qt
)

from download import download_ffmpeg, process_instagram_downloads_from_profiles, process_youtube_downloads_from_profiles, \
    process_twitter_downloads_from_profiles
from ui import MainWindow, app
from upload import upload_random_instagram_video, insta_session_thread, \
    twitter_session_thread, twitter_session_refresh_thread
from utils import setup_logging, create_directory, clean_temp_files, dummy_module, DummyVideoFileClip, \
    setup_configurations

PROJECT_ROOT = Path(__file__).resolve().parent

# 2. Platform Tespiti İçin Fonksiyon Ekleyin


# 3. İndirme ve Çıkarma Fonksiyonunu Ekleyin


# Yüksek DPI ölçeklendirmesi için gerekli ayarlar (QApplication oluşturulmadan önce):
QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
QCoreApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
# Windows'ta 125% veya 150% gibi ara ölçek değerlerinin aynen kullanılmasını sağla
os.environ["QT_SCALE_FACTOR_ROUNDING_POLICY"] = "PassThrough"

# GPU'suz sistemlerde yazılım OpenGL render kullanımı için gerekli ayarlar:
os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--disable-gpu --disable-software-rasterizer"
QCoreApplication.setAttribute(Qt.AA_UseSoftwareOpenGL)

# ----------------------------------------------------------------------------------
# TELEGRAM BİLGİLERİNİ BURAYA GİRİN (TOKEN VE CHAT_ID):
# ----------------------------------------------------------------------------------


# ----------------------------------------------------------------------------------

# ----------------------------------------------------------------------------------
# İSTATİSTİK SİSTEMİ DEĞİŞKENLERİ
# ----------------------------------------------------------------------------------


# Global istatistik sistemi nesnesi


# ---------- Dummy moviepy.editor modülü oluşturma ----------


# Eğer "moviepy.editor" yüklü değilse dummy modülü ekleyelim
if "moviepy.editor" not in sys.modules:
    dummy_module.VideoFileClip = DummyVideoFileClip
    sys.modules["moviepy.editor"] = dummy_module


# ---------- ffmpeg İndirme Fonksiyonu ----------


# ---------- Loglama ve Klasör İşlemleri ----------


# ----------------------------------------------------------------------------------
# İSTATİSTİK ÇEKME FONKSİYONLARI
# ----------------------------------------------------------------------------------


# ----------------------------------------------------------------------------------
# İSTATİSTİK SİSTEMİ YARDIMCI FONKSİYONLARI
# ----------------------------------------------------------------------------------


# ---------- Instagram Video İndirme Fonksiyonları ----------


# ---------- YouTube Video İndirme Fonksiyonları ----------


# ----------------------------------------------------------------------
# ARTIK LINK KAYNAĞI OLARAK PROFİLLERİN .JSON DOSYASI KULLANILACAK
# ----------------------------------------------------------------------


# ---------- Instagram Video Yükleme (Rastgele Seçim) ----------


# <<< YENİ EKLENEN >>>


# ---------- Configuration Dosyalarının Oluşturulması ----------


# ---------- Konsol Tabanlı Ana Program (Artık GUI ile entegre) ----------
def console_main():
    logger = setup_logging()
    base_dir = PROJECT_ROOT
    videos_dir = base_dir / "videos"
    os.makedirs(videos_dir, exist_ok=True)

    instagram_output_dir = videos_dir / "instagramdownloaded"
    youtube_output_dir = videos_dir / "youtubedownloaded"
    twitter_output_dir = videos_dir / "twitterdownloaded"
    temp_dir = videos_dir / "temp"

    create_directory(str(instagram_output_dir))
    create_directory(str(youtube_output_dir))
    create_directory(str(twitter_output_dir))
    create_directory(str(temp_dir))

    setup_configurations()

    print("Lütfen yapmak istediğiniz işlemi seçin:")
    print("1 - Video İndirme (Artık .txt yerine profil JSON dosyalarındaki linkler kullanılır)")
    print("2 - Instagram Video Paylaşma (instagramdownloaded klasöründen rastgele video)")
    print("3 - Twitter Video Tweetleme (twitterdownloaded klasöründen rastgele video)")
    choice = input("Seçiminiz (1, 2 veya 3): ").strip()

    if choice == "1":
        try:
            ffmpeg_dir = download_ffmpeg()
            if not ffmpeg_dir:
                logging.error("ffmpeg kurulamadı, YouTube/Twitter videoları indirilemeyecek")
                return
            # JSON dosyalarından okunan linkler üzerinden indirme işlemleri
            process_instagram_downloads_from_profiles(str(instagram_output_dir), str(temp_dir))
            process_youtube_downloads_from_profiles(str(youtube_output_dir), ffmpeg_dir)
            process_twitter_downloads_from_profiles(str(twitter_output_dir), ffmpeg_dir)
        except Exception as e:
            logging.error(f"Ana program hatası: {str(e)}")
        finally:
            clean_temp_files(str(temp_dir))
    elif choice == "2":
        upload_random_instagram_video(str(instagram_output_dir))
    elif choice == "3":
        upload_random_twitter_video(str(twitter_output_dir))
    else:
        print("Geçersiz seçim. Lütfen 1, 2 veya 3 girin.")


if __name__ == "__main__":
    logger = setup_logging()
    setup_configurations()

    # Start Instagram session initialization in a background thread
    insta_session_thread.start()

    # Start Twitter session initialization in a background thread
    twitter_session_thread.start()

    # Twitter oturum meta dosyalarını periyodik kontrol eden thread

    twitter_session_refresh_thread.start()

    window = MainWindow()
    window.show()
    sys.exit(app.exec_())